# Bug Fix CI Verification Protocol

## Purpose
To establish a standardized protocol for the correct order of operations when handling bug fixes, ensuring both automated verification and efficient manual testing cycles.

## Workflow Overview

### 1. Bug Report & Assignment
- A bug is reported and logged by the tester.
- The bug is assigned to a specific developer based on module ownership.

### 2. Code Fix & Test Creation
- Dev<PERSON>per writes the fix.
- <PERSON><PERSON><PERSON> creates a corresponding test file named in the following format:

```
test_<feature>_<bug_description>_BUG-XXX.py
```

**Examples:**
- `test_client_save_missing_required_field_BUG-003.py`
- `test_invoice_submit_no_items_BUG-014.py`

### 3. CI Pipeline Validation
- Developer submits a PR with:
  - Descriptive PR title (e.g.):
    - `fix: client save fails without email (BUG-003)`
    - `fix: invoice submit crashes with no items (BUG-014)`
    - `fix: login fails due to hashing error (BUG-021)`
  - Bug-referencing commit messages
  - New or updated test file
- The CI pipeline runs automatically.
- Only the test for the bug being fixed must pass.
- Other unrelated failing tests do **not** block this PR.

### 4. PR Review & Merge
- PR is reviewed by HQ or QA Lead.
- If the fix is valid and the specific bug test passes in CI, the PR is approved and merged.

### 5. Build Scheduling for Manual Verification
- Once **3–5 bug fix PRs have passed CI**, a new build is created.
- The build is deployed for tester to manually verify the fixed functionality.
- Tester marks each verified bug as `Verified` in the tracker.

---

###  Bug Fix Verification 

All bug fix pull requests must include a **Bug Verification** section in the PR description. This is mandatory for visual confirmation and accountability.

####  Requirements:

* **Frontend/UI Bug Fixes**

  * Must include at least one **screenshot** (or GIF) showing the fixed bug behavior.
  * The screenshot must clearly display the corrected issue.
  * If the bug is visible only under certain roles (e.g., attorney), the screenshot must show the correct user view.

* **Backend-Only Bug Fixes**

  * Must include a short note stating:
    `"This bug fix is backend-only; no UI changes were made."`

####  PR Template Addition:

Developers must fill out the following section in their PRs:

##  Bug Fix Verification

- [ ] Screenshot or GIF attached (if applicable)
- [ ] Description of what was broken and how it was fixed
- [ ] [If backend-only] No UI screenshot required — backend logic fix only
```

---

Failure to comply with this section shall result in **PR rejection** or **reversion post-merge**.


## PR Naming Format

### PR Title:
Provide a short but clear description of the fix, including the bug number.

**Examples:**
- `fix: client save fails without email (BUG-003)`
- `fix: invoice submit crashes with no items (BUG-014)`
- `fix: login fails with correct credentials due to hashing error (BUG-021)`

```
fix: <description of bug> (BUG-XXX)
```

### Commit Message:
```
fix: <what was fixed> (BUG-XXX)
test: <describe test that validates fix> (BUG-XXX)
```

## Enforcement
- Manual verification **only occurs** after passing CI.
- Test files must reference the bug number.
- No PRs are approved without passing the bug-specific test in CI.

---


Last Udpated
Stardate: 2025.216
The_Bossman_CLG 
