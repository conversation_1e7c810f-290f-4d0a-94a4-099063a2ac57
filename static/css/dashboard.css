/* Enhanced Attorney Dashboard CSS - Optimized */
:root {
  --primary-color: #2c3e50;
  --primary-dark: #1a252f;
  --primary-light: #34495e;
  --secondary-color: #3498db;
  --secondary-dark: #2980b9;
  --accent-color: #e74c3c;
  --success-color: #2ecc71;
  --warning-color: #f39c12;
  --text-color: #333;
  --text-light: #7f8c8d;
  --bg-color: #f5f7fa;
  --sidebar-bg: #2c3e50;
  --sidebar-text: #ecf0f1;
  --sidebar-hover: #34495e;
  --white: #ffffff;
  --gray-light: #ecf0f1;
  --gray-medium: #bdc3c7;
  --gray-dark: #95a5a6;
  --border-color: #ddd;
  --max-width: 1400px;
  --transition-speed: 0.3s;
  --border-radius: 6px;
  --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  --font-family: "Segoe UI", "Roboto", "Helvetica Neue", sans-serif;
}

/* Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  background-color: var(--bg-color);
  color: var(--text-color);
  line-height: 1.6;
}

a {
  text-decoration: none;
  color: inherit;
}

/* Layout */
.main-container {
  display: flex;
  min-height: 100vh;
}

.content-area {
  flex: 1;
  padding: 20px;
  margin-left: 250px; /* Sidebar width */
}

/* Top Navigation */
.top-nav {
  background-color: var(--primary-color);
  color: white;
  padding: 0 20px;
  position: fixed;
  width: 100%;
  z-index: 1000;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: var(--max-width);
  margin: 0 auto;
  height: 60px;
}

.nav-brand {
  display: flex;
  align-items: center;
  font-size: 1.5rem;
  font-weight: 600;
}

.nav-brand i {
  margin-right: 10px;
  font-size: 1.8rem;
}

.quick-action-modal {
  display: none;
  position: fixed;
  z-index: 1001;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0,0,0,0.5);
}

.modal-content {
  background-color: #fff;
  margin: 10% auto;
  padding: 20px;
  border-radius: 8px;
  width: 50%;
  max-width: 600px;
  position: relative;
}

.close-btn {
  position: absolute;
  top: 10px;
  right: 20px;
  font-size: 22px;
  cursor: pointer;
}


.nav-links {
  display: flex;
  align-items: center;
}

.nav-links a {
  padding: 0 15px;
  height: 60px;
  display: flex;
  align-items: center;
  transition: background-color var(--transition-speed);
}

.nav-links a:hover,
.nav-links a.active {
  background-color: var(--primary-dark);
}

.nav-links a i {
  margin-right: 8px;
}

.user-dropdown {
  position: relative;
  display: inline-block;
}

.user-btn {
  background: none;
  border: none;
  color: white;
  display: flex;
  align-items: center;
  padding: 0 15px;
  height: 60px;
  cursor: pointer;
  font-family: inherit;
  font-size: inherit;
}

.user-btn i {
  margin-right: 8px;
  font-size: 1.2rem;
}

.dropdown-content {
  display: none;
  position: absolute;
  right: 0;
  background-color: white;
  min-width: 200px;
  box-shadow: var(--box-shadow);
  z-index: 1;
  border-radius: var(--border-radius);
  overflow: hidden;
}

.dropdown-content a {
  color: var(--text-color);
  padding: 12px 16px;
  display: block;
  transition: background-color var(--transition-speed);
}

.dropdown-content a:hover {
  background-color: var(--gray-light);
}

.user-dropdown:hover .dropdown-content {
  display: block;
}

/* Sidebar */
.sidebar {
  width: 250px;
  background-color: var(--sidebar-bg);
  color: var(--sidebar-text);
  position: fixed;
  height: 100vh;
  padding-top: 60px; /* Account for top nav */
  overflow-y: auto;
  transition: transform var(--transition-speed);
  z-index: 900;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid var(--sidebar-hover);
}

.sidebar-menu {
  list-style: none;
}

.sidebar-menu li {
  border-bottom: 1px solid var(--sidebar-hover);
}

.sidebar-menu li a {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  transition: background-color var(--transition-speed);
}

.sidebar-menu li a i {
  margin-right: 10px;
  width: 20px;
  text-align: center;
}

.sidebar-menu li a:hover,
.sidebar-menu li.active a {
  background-color: var(--sidebar-hover);
  color: white;
}

/* Dashboard Header */
.dashboard-header {
  margin-bottom: 30px;
  padding-top: 20px;
}

.dashboard-header h1 {
  font-size: 2rem;
  color: var(--primary-color);
  margin-bottom: 5px;
}

.dashboard-header p {
  color: var(--text-light);
  font-size: 1rem;
}

/* Section Styles */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
}

.section-header h2 {
  font-size: 1.5rem;
  color: var(--primary-color);
  display: flex;
  align-items: center;
}

.section-header h2 i {
  margin-right: 10px;
  color: var(--secondary-color);
}

.section-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-box i {
  position: absolute;
  left: 10px;
  color: var(--gray-dark);
}

.search-box input {
  padding: 8px 10px 8px 35px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  width: 200px;
  transition: width var(--transition-speed);
}

.search-box input:focus {
  outline: none;
  border-color: var(--secondary-color);
  width: 250px;
}

/* Button Styles */
.btn {
  padding: 8px 16px;
  border-radius: var(--border-radius);
  border: none;
  cursor: pointer;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-speed);
}

.btn i {
  margin-right: 8px;
}

.btn-primary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--secondary-dark);
}

.btn-secondary {
  background-color: var(--gray-light);
  color: var(--text-color);
}

.btn-secondary:hover {
  background-color: var(--gray-medium);
}

/* Table Styles */
.table-responsive {
  overflow-x: auto;
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

table {
  width: 100%;
  border-collapse: collapse;
}

th,
td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

th {
  background-color: var(--primary-color);
  color: white;
  font-weight: 500;
}

tr:hover {
  background-color: rgba(52, 152, 219, 0.1);
}

/* Case Layout */
.case-layout {
  display: flex;
  gap: 20px;
}

.case-list-container {
  flex: 1;
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: 15px;
}

.case-filters {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.filter-group {
  display: flex;
  align-items: center;
}

.filter-group label {
  margin-right: 8px;
  font-size: 0.9rem;
  color: var(--text-light);
}

.filter-group select {
  padding: 6px 10px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: white;
}

.case-list {
  list-style: none;
  max-height: 600px;
  overflow-y: auto;
}

.case-item {
  padding: 15px;
  margin-bottom: 10px;
  border-radius: var(--border-radius);
  background: white;
  border: 1px solid var(--border-color);
  cursor: pointer;
  transition: all var(--transition-speed);
}

.case-item:hover {
  border-color: var(--secondary-color);
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.2);
}

.case-item.active {
  border-left: 4px solid var(--secondary-color);
  background-color: rgba(52, 152, 219, 0.05);
}

.case-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.case-name {
  font-weight: 600;
  color: var(--primary-color);
}

.case-status {
  font-size: 0.75rem;
  padding: 3px 8px;
  border-radius: 12px;
  text-transform: capitalize;
}

.case-status.open {
  background: #e3f2fd;
  color: #1976d2;
}

.case-status.closed {
  background: #e8f5e9;
  color: #388e3c;
}

.case-details {
  display: flex;
  gap: 10px;
  font-size: 0.85rem;
  color: var(--text-light);
}

.case-type {
  padding-right: 10px;
  border-right: 1px solid var(--border-color);
}

.client-name {
  font-style: italic;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-light);
}

.empty-state i {
  font-size: 3rem;
  color: var(--gray-medium);
  margin-bottom: 15px;
}

/* Overview Grid */
.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.overview-card {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--primary-color);
  color: white;
}

.card-header h3 {
  font-size: 1.1rem;
  display: flex;
  align-items: center;
}

.card-header h3 i {
  margin-right: 10px;
}

.view-all,
.mark-all-read {
  font-size: 0.8rem;
  color: white;
  background: none;
  border: none;
  cursor: pointer;
  text-decoration: underline;
}

.card-content {
  padding: 20px;
  cursor: pointer;
}

.document-item:hover{
  background-color: var(--gray-medium);
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(52, 152, 219, 0.2);
  border-radius: 50%;
  border-top-color: var(--secondary-color);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.quick-action-btn {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 10px 15px;
  margin-bottom: 10px;
  background-color: var(--gray-light);
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: background-color var(--transition-speed);
}

.quick-action-btn i {
  margin-right: 10px;
  color: var(--white);
}

.quick-action-btn:hover {
  background-color: var(--gray-medium);
}

.time-summary,
.billing-stats,
.client-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.time-metric,
.stat-item {
  text-align: center;
}

.time-value,
.stat-value {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--primary-color);
}

.time-label,
.stat-label {
  font-size: 0.8rem;
  color: var(--text-light);
}

.chart-placeholder {
  width: 150px;
  height: 80px;
  background-color: var(--gray-light);
  border-radius: var(--border-radius);
}

/* Notification Items */
.notification-items {
  list-style: none;
}

.notification-item {
  padding: 12px 0;
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: background-color var(--transition-speed);
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item:hover {
  background-color: rgba(52, 152, 219, 0.05);
}

.notification-item.unread {
  background-color: rgba(52, 152, 219, 0.1);
}

.notification-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.notification-type {
  font-weight: bold;
  text-transform: capitalize;
  font-size: 0.9rem;
}

.notification-type.deadline {
  color: var(--accent-color);
}

.notification-type.message {
  color: var(--secondary-color);
}

.notification-message {
  margin: 0;
  font-size: 0.9rem;
  color: var(--text-color);
}

.notification-date {
  font-size: 0.75rem;
  color: var(--text-light);
}

/* Deadline Colors */
.deadline-item {
  border-left: 4px solid var(--secondary-color);
  margin-bottom: 10px;
  padding: 10px;
  background: white;
  border-radius: var(--border-radius);
}

.deadline-item.urgent {
  border-left-color: var(--accent-color);
  background-color: rgba(231, 76, 60, 0.05);
}

.deadline-item.upcoming {
  border-left-color: var(--warning-color);
  background-color: rgba(243, 156, 18, 0.05);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .case-layout {
    flex-direction: column;
  }
}

@media (max-width: 992px) {
  .sidebar {
    transform: translateX(-100%);
    position: fixed;
    top: 60px;
    left: 0;
    height: calc(100vh - 60px);
  }

  .content-area {
    margin-left: 0;
  }

  .sidebar.active {
    transform: translateX(0);
  }

  .nav-links a span,
  .user-btn span {
    display: none;
  }

  .nav-links a i,
  .user-btn i {
    margin-right: 0;
    font-size: 1.3rem;
  }
}

@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .section-actions {
    width: 100%;
    justify-content: space-between;
  }

  .search-box input {
    width: 100%;
  }

  .overview-grid {
    grid-template-columns: 1fr;
  }
}

/* Toast Notification */
.notification-toast {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: var(--primary-color);
  color: white;
  padding: 12px 20px;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  z-index: 1000;
  display: flex;
  align-items: center;
  animation: slide-in 0.3s;
}

.notification-toast i {
  margin-right: 10px;
  font-size: 1.2rem;
}

.notification-toast.fade-out {
  animation: fade-out 0.3s;
}

@keyframes slide-in {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

/* Simplified Clients Table */
#clients-table th,
#clients-table td {
  padding: 10px;
}
#clients-table td {
width: 100%;
display: flex;
align-items: center;
justify-content: space-between;
}

#clients-table th:first-child,
#clients-table td:first-child {
  width: 100%;
}

#clients-table th:not(:first-child),
#clients-table td:not(:first-child) {
  display: none;
}

/* Quick Actions Styling */
.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 15px;
  padding: 15px;
}

.quick-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px 10px;
  border-radius: 8px;
  background: var(--primary-light);
  color: var(--white);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 80px;
  text-align: center;
  font-weight: 500;
}

.quick-action-btn:hover {
  background: var(--secondary-color);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.quick-action-btn i {
  font-size: 24px;
  margin-bottom: 8px;
}

/* Modal Styling */
.quick-action-modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.5);
}

.modal-content {
  background-color: white;
  margin: 5% auto;
  padding: 25px;
  border-radius: 10px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.3);
  position: relative;
}

.close-btn {
  position: absolute;
  right: 20px;
  top: 15px;
  font-size: 24px;
  cursor: pointer;
  color: #aaa;
}

.close-btn:hover {
  color: #333;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.form-group input[type="text"],
.form-group input[type="datetime-local"],
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.form-row {
  display: flex;
  gap: 15px;
}

.form-row .form-group {
  flex: 1;
}

.submit-btn {
  background: var(--secondary-color);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  width: 100%;
  margin-top: 10px;
}

.submit-btn:hover {
  background: var(--primary-dark);
}

/* Mobile Menu Button */
.menu-toggle {
  display: none;
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0 15px;
}

/* Responsive Navigation */
@media (max-width: 992px) {
  .menu-toggle {
    display: block;
  }
  
  .nav-links {
    position: fixed;
    top: 60px;
    left: 0;
    width: 100%;
    background: var(--primary-dark);
    flex-direction: column;
    align-items: stretch;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
  }
  
  .nav-links.active {
    max-height: 500px;
    padding: 15px 0;
    box-shadow: 0 5px 10px rgba(0,0,0,0.2);
  }
  
  .nav-links a {
    padding: 12px 20px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
  }
  
  .user-dropdown {
    width: 100%;
  }
  
  .user-btn {
    width: 100%;
    justify-content: flex-start;
    padding: 12px 20px;
  }
  
  .dropdown-content {
    position: static;
    width: 100%;
    box-shadow: none;
    border-radius: 0;
  }
  
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .sidebar.active {
    transform: translateX(0);
  }
  
  .content-area {
    margin-left: 0;
    padding-top: 80px;
  }
  
  /* Make tables horizontally scrollable on mobile */
  .table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    display: block;
    width: 100%;
  }
  
  #clients-table, #contacts-table {
    min-width: 600px; /* Forces horizontal scroll */
  }
}

@media (max-width: 768px) {
  .dashboard-header h1 {
    font-size: 1.5rem;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .section-actions {
    width: 100%;
    flex-direction: column;
    gap: 10px;
  }
  
  .search-box input {
    width: 100%;
  }
  
  .quick-actions-grid {
    grid-template-columns: 1fr 1fr;
  }
  
  .modal-content {
    width: 95%;
    margin: 10% auto;
  }
}

@media (max-width: 480px) {
  .quick-actions-grid {
    grid-template-columns: 1fr;
  }
  
  .case-filters {
    flex-direction: column;
    gap: 10px;
  }
  
  .filter-group {
    width: 100%;
  }
  
  .filter-group select {
    width: 100%;
  }
}