document.addEventListener("DOMContentLoaded", () => {
    // Initialize socket.io
    const socket = io();

    // Set current date
    const options = {
        weekday: "long",
        year: "numeric",
        month: "long",
        day: "numeric",
    };
    const today = new Date();
    document.getElementById("current-date").textContent =
        today.toLocaleDateString("en-US", options);

    // Tab switching
    const tabLinks = document.querySelectorAll(".tab-link");
    const tabContents = document.querySelectorAll(".tab-content");

    tabLinks.forEach((link) => {
        link.addEventListener("click", (e) => {
            e.preventDefault();
            const targetTabId = e.target.getAttribute("data-tab");
            switchTab(targetTabId);
        });
    });

    // Initialize cases tab with overview button functionality
    initCasesTab();

    // Initial data load - force load overview data immediately
    loadInitialData();
    fetchDashboardData(); // Explicitly load overview data

    // Setup socket.io listeners
    setupSocketListeners(socket);

    // Setup event listeners for buttons
    setupEventListeners();

    initializeQuickActions();
});

/**
 * Switches between tabs and loads appropriate data
 * @param {string} targetTabId - The ID of the tab to switch to
 */
function switchTab(targetTabId) {
    const tabLinks = document.querySelectorAll(".tab-link");
    const tabContents = document.querySelectorAll(".tab-content");

    tabLinks.forEach((btn) => btn.classList.remove("active"));
    tabContents.forEach((tab) => tab.classList.remove("active"));

    // Highlight active tab in sidebar
    document.querySelectorAll(".sidebar-menu li").forEach((item) => {
        item.classList.remove("active");
        if (item.querySelector(`a[data-tab="${targetTabId}"]`)) {
            item.classList.add("active");
        }
    });

    document.getElementById(targetTabId).classList.add("active");

    // Load appropriate data
    if (targetTabId === "contacts-tab") {
        fetchContacts();
    } else if (targetTabId === "cases-tab") {
        fetchCases();
    } else if (targetTabId === "overview-tab") {
        fetchDashboardData(); // Ensure data is loaded when tab is clicked
    }
}

/**
 * Initializes the cases tab with click handlers
 */
function initCasesTab() {
    document.querySelectorAll(".case-item").forEach((item) => {
        const caseId = item.dataset.caseId;

        item.onclick = () => {
            window.location.href = `/case/${caseId}/details`;
        };

        // --- Overview Button ---
        const overviewBtn = document.createElement("button");
        overviewBtn.className = "btn btn-secondary btn-sm overview-btn";
        overviewBtn.innerHTML = '<i class="fas fa-chart-bar"></i> Overview';
        overviewBtn.onclick = (e) => {
            e.stopPropagation();
            window.location.href = `/case-overview?case_id=${caseId}&tab=activities`;
        };

        // --- Update Button ---
        const updateBtn = document.createElement("button");
        updateBtn.className = "btn btn-primary btn-sm update-case-btn ms-2";
        updateBtn.innerHTML = '<i class="fas fa-edit"></i> Update';
        updateBtn.onclick = (e) => {
            e.stopPropagation();
            window.location.href = `/update-case-form?case_id=${caseId}`;
        };

        const actionsDiv =
            item.querySelector(".case-actions") || document.createElement("div");
        actionsDiv.className = "case-actions";
        actionsDiv.appendChild(overviewBtn);
        actionsDiv.appendChild(updateBtn);
        item.appendChild(actionsDiv);
    });
}

/**
 * Loads initial data for the active tab
 */
function loadInitialData() {
    // Load clients
    fetch("/attorney/clients")
        .then((response) => response.json())
        .then((clients) => {
            populateClients(clients);
            populateClientOverview(clients);
        })
        .catch((error) => console.error("Error fetching clients:", error));

    // Load contacts automatically like clients
    fetchContacts();

    // Load initial tab data
    const activeTab = document.querySelector(".tab-content.active").id;
    //   if (activeTab === "contacts-tab") {
    //     fetchContacts();
    //   } else
    if (activeTab === "cases-tab") {
        fetchCases();
    }
}

/**
 * Populates client overview statistics
 * @param {Array} clients - Array of client objects
 */
function populateClientOverview(clients) {
    const activeCount = Array.isArray(clients) ? clients.length : 0;
    document.querySelector("#client-overview .client-stats").innerHTML = `
        <div class="stat-item">
            <span class="stat-value">${activeCount}</span>
            <span class="stat-label">Active Clients</span>
        </div>
    `;
}

/**
 * Initializes quick action modals and forms
 */
function initializeQuickActions() {
    // Task form submission
    const taskForm = document.getElementById('taskForm');
    if (taskForm) {
        taskForm.addEventListener('submit', function (e) {
            e.preventDefault();
            createTask();
        });
    }

    // Deadline form submission
    const deadlineForm = document.getElementById('deadlineForm');
    if (deadlineForm) {
        deadlineForm.addEventListener('submit', function (e) {
            e.preventDefault();
            createDeadline();
        });
    }

    // Populate case dropdowns
    fetch('/api/get-all-cases-by-attorney')
        .then(response => response.json())
        .then(cases => {
            const caseSelects = document.querySelectorAll('#taskCaseId, #deadlineCaseId');
            caseSelects.forEach(select => {
                // Clear existing options
                select.innerHTML = '<option value="">Select a case</option>';

                cases.forEach(c => {
                    const option = document.createElement('option');
                    option.value = c.case_id;
                    option.textContent = c.case_name || 'Untitled Case';
                    select.appendChild(option);
                });
            });
        })
        .catch(error => console.error('Error fetching cases for dropdown:', error));
}

/**
 * Shows the quick action modal for different actions
 * @param {string} action - The action type (event, document, message, etc.)
 */
function showQuickActionModal(action) {
    const modal = document.getElementById('quickActionModal');
    const title = document.getElementById('quickActionTitle');
    const content = document.getElementById('quickActionContent');

    const actionTitles = {
        'event': 'Create Event',
        'document': 'Upload Document',
        'message': 'Send Message',
        'time': 'Log Time Entry',
        'expense': 'Add Expense',
        'invoice': 'Create Invoice',
        'note': 'Add Note'
    };

    title.innerHTML = `<i class="fas fa-${getIconForAction(action)}"></i> ${actionTitles[action]}`;
    content.innerHTML = `<p>${actionTitles[action]} form will appear here. This is a placeholder.</p>`;

    modal.style.display = 'block';
}

window.showQuickActionModal = showQuickActionModal;


/**
 * Shows the task creation modal
 */
function showTaskModal() {
    document.getElementById("taskModal").style.display = "block";
}

// Attach to the global `window` object
window.showTaskModal = showTaskModal;

/**
 * Shows the deadline creation modal
 */
function showDeadlineModal() {
    document.getElementById("deadlineModal").style.display = "block";
}

// Attach to the global `window` object
window.showDeadlineModal = showDeadlineModal;

/**
 * Closes a modal by ID
 * @param {string} modalId - The ID of the modal to close
 */
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = "none";
    } else {
        console.error(`Modal with ID "${modalId}" not found.`);
    }
}

// Attach to the global `window` object
window.closeModal = closeModal;

/**
 * Gets the appropriate icon for an action type
 * @param {string} action - The action type
 * @returns {string} The icon class name
 */
function getIconForAction(action) {
    const icons = {
        event: "calendar-check",
        document: "file-alt",
        message: "envelope",
        time: "stopwatch",
        expense: "receipt",
        invoice: "file-invoice-dollar",
        note: "sticky-note",
    };
    return icons[action] || "plus-circle";
}


/**
 * Creates a new task via API
 */
async function createTask() {
    const formData = {
        case_id: document.getElementById('taskCaseId').value,
        task_type: document.getElementById('taskType').value,
        description: document.getElementById('taskDescription').value,
        due_date: document.getElementById('taskDueDate').value,
        priority: document.getElementById('taskPriority').value,
        status: 'pending'
    };

    try {
        const response = await fetch('/api/tasks', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });

        const result = await response.json();

        if (response.ok) {
            showToast('Task created successfully!');
            closeModal('taskModal');
            document.getElementById('taskForm').reset();
            // Refresh relevant data
            if (document.querySelector('.tab-content.active').id === 'cases-tab') {
                fetchCases();
            }
            fetchDashboardData();
        } else {
            showToast(`Error: ${result.error}`);
        }
    } catch (error) {
        showToast('Failed to create task: ' + error.message);
    }
}

/**
 * Creates a new deadline via API
 */
async function createDeadline() {
    const formData = {
        case_id: document.getElementById('deadlineCaseId').value,
        deadline_type: document.getElementById('deadlineType').value,
        deadline_date: document.getElementById('deadlineDate').value,
        notes: document.getElementById('deadlineNotes').value,
        is_court_date: document.getElementById('isCourtDate').checked
    };

    try {
        const response = await fetch('/api/deadlines', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });

        const result = await response.json();

        if (response.ok) {
            showToast('Deadline added successfully!');
            closeModal('deadlineModal');
            document.getElementById('deadlineForm').reset();
            // Refresh relevant data
            fetchDeadlines();
            if (document.querySelector('.tab-content.active').id === 'cases-tab') {
                fetchCases();
            }
        } else {
            showToast(`Error: ${result.error}`);
        }
    } catch (error) {
        showToast('Failed to add deadline: ' + error.message);
    }
}

/**
 * Fetches and populates client data
 */
function populateClients(clients) {
    const tableBody = document.getElementById("clients-list");
    tableBody.innerHTML = "";

    if (!Array.isArray(clients) || clients.length === 0) {
        tableBody.innerHTML = `
        <tr>
          <td colspan="1" class="empty-state">
            <i class="fas fa-users"></i>
            <p>No clients found</p>
          </td>
        </tr>
      `;
        return;
    }

    clients.forEach((client) => {
        const row = document.createElement("tr");
        row.innerHTML = `
        <td>${client.name || "N/A"}
            <button class="btn btn-sm btn-primary update-client-btn" data-client-id="${client.id
            }">
                <i class="fas fa-edit"></i> Update
            </button>
        </td>
        <td>
            <button class="btn btn-sm btn-primary update-client-btn" data-client-id="${client.id
            }">
                <i class="fas fa-edit"></i> Update
            </button>
        </td>
      `;
        tableBody.appendChild(row);
    });

    // Attach click handlers to update buttons
    document.querySelectorAll(".update-client-btn").forEach((button) => {
        button.addEventListener("click", (e) => {
            const clientId = e.target.closest("button").dataset.clientId;
            window.location.href = `/update-client-form?client_id=${clientId}`;
        });
    });
}

/**
 * Fetches cases data from the server
 * @returns {Promise} Promise that resolves with cases data
 */
function fetchCases() {
    return fetch("/api/get-all-cases-by-attorney")
        .then((response) => response.json())
        .then((cases) => {
            renderCases(cases);
            return cases; // Return cases for chaining
        })
        .catch((error) => {
            console.error("Error fetching cases:", error);
            showError("cases-list", "Failed to load cases");
            return []; // Return empty array in case of error
        });
}

/**
 * Renders cases in the cases list
 * @param {Array} cases - Array of case objects
 */
function renderCases(cases) {
    const container = document.getElementById("cases-list");
    container.innerHTML = "";

    if (!cases || cases.length === 0) {
        container.innerHTML = `
        <div class="empty-state">
          <i class="fas fa-folder-open"></i>
          <p>No cases found</p>
        </div>
      `;
        return;
    }

    cases.forEach((caseItem) => {
        const li = document.createElement("li");
        li.className = "case-item";
        li.dataset.caseId = caseItem.case_id;
        li.innerHTML = `
        <div class="case-header">
          <span class="case-name">${caseItem.case_name || "Untitled Case"
            }</span>
          <span class="case-status ${caseItem.case_status.toLowerCase()}">${caseItem.case_status
            }</span>
        </div>
        <div class="case-details">
          <span class="case-type">${caseItem.case_type}</span>
          <span class="client-name">${caseItem.client_name}</span>
        </div>
        <div class="case-actions"></div>
      `;
        container.appendChild(li);
    });

    // Reinitialize case buttons after rendering
    initCasesTab();
}

/**
 * Fetches contacts data from the server
 */
async function fetchContacts() {
  try {
    // Fetch both staff contacts and external contacts in parallel
    const [staffContacts, externalContacts] = await Promise.all([
      fetch("/api/contacts").then(res => res.json()),
      fetch("/api/external-contacts").then(res => res.json())
    ]);

    // Combine the contacts
    const allContacts = [
      ...(Array.isArray(staffContacts) ? staffContacts : []),
      ...(externalContacts.contacts || [])
    ];

    populateContactsTable(allContacts);
  } catch (err) {
    console.error("Error fetching contacts:", err);
    showError("contacts-tab", "Failed to load contacts");
  }
}

/**
 * Populates the contacts table
 * @param {Array} contacts - Array of contact objects
 */
function populateContactsTable(contacts) {
    const tableBody = document.querySelector("#contacts-table tbody");
    tableBody.innerHTML = "";

    if (!Array.isArray(contacts) || contacts.length === 0) {
        tableBody.innerHTML = `
        <tr>
          <td colspan="7" class="empty-state">
            <i class="fas fa-address-book"></i>
            <p>No contacts found</p>
          </td>
        </tr>
      `;
        return;
    }

  contacts.forEach((contact) => {
    const row = document.createElement("tr");
    const {
      name = "N/A",
      contact_type = "N/A",
      email = "N/A",
      phone = "N/A",
      case_link = "N/A",
      last_contact = "N/A",
      role = "N/A",
      id
    } = contact;

        row.innerHTML = `
        <td>${name}</td>
        <td>${contact_type}</td>
        <td>${email}</td>
        <td>${formatPhoneNumber(phone)}</td>
        <td>${case_link}</td>
        <td>${last_contact === "N/A" ? "N/A" : formatDate(last_contact)}</td>
        <td class="actions">
          <button class="btn-icon edit-contact" data-contact-id="${id}" title="Edit">
            <i class="fas fa-edit"></i>
          </button>
          <button class="btn-icon delete-contact" data-contact-id="${id}" title="Delete">
            <i class="fas fa-trash"></i>
          </button>
        </td>
      `;
    tableBody.appendChild(row);
  });

  // // Add event listeners for the action buttons
  // document.querySelectorAll('.edit-contact').forEach(btn => {
  //   btn.addEventListener('click', (e) => {
  //     const contactId = e.currentTarget.dataset.contactId;
  //     window.location.href = `/edit-contact/${contactId}`;
  //   });
  // });

  document.querySelectorAll('.delete-contact').forEach(btn => {
    btn.addEventListener('click', async (e) => {
      const contactId = e.currentTarget.dataset.contactId;
      if (confirm('Are you sure you want to delete this contact?')) {
        try {
          const response = await fetch(`/api/external-contacts/${contactId}`, {
            method: 'DELETE'
          });
          
          if (response.ok) {
            showToast('Contact deleted successfully');
            fetchContacts(); // Refresh the list
          } else {
            const error = await response.json();
            showToast(`Error: ${error.error}`);
          }
        } catch (err) {
          showToast('Failed to delete contact');
        }
      }
    });
  });
}

/**
 * Fetches all dashboard overview data
 */
function fetchDashboardData() {
    fetchDeadlines();
    fetchActiveCasesOnly();
    fetchAndRenderNotifications();
    loadAttorneyDocumentActivity();
}

/**
 * Fetches deadlines data
 */
function fetchDeadlines() {
    fetch("/api/attorney/deadlines")
        .then((response) => response.json())
        .then((deadlines) => renderDeadlines(deadlines))
        .catch((error) => {
            console.error("Error fetching deadlines:", error);
            showError("deadlines-list", "Failed to load deadlines");
        });
}

/**
 * Renders deadlines with color coding
 * @param {Array} deadlines - Array of deadline objects
 */
function renderDeadlines(deadlines) {
    const container = document.getElementById("deadlines-list");
    container.innerHTML = "";

    if (!deadlines || deadlines.length === 0) {
        container.innerHTML = `
        <div class="empty-state">
          <i class="fas fa-calendar-check"></i>
          <p>No upcoming deadlines</p>
        </div>
      `;
        return;
    }

    const ul = document.createElement("ul");
    ul.classList.add("deadline-items");

    // Sort by date
    deadlines.sort(
        (a, b) => new Date(a.deadline_date) - new Date(b.deadline_date)
    );

    deadlines.slice(0, 5).forEach((deadline) => {
        const li = document.createElement("li");

        // Determine deadline urgency
        const deadlineDate = new Date(deadline.deadline_date);
        const today = new Date();
        const daysUntilDeadline = Math.floor(
            (deadlineDate - today) / (1000 * 60 * 60 * 24)
        );

        let deadlineClass = "";
        if (daysUntilDeadline <= 3) {
            deadlineClass = "urgent";
        } else if (daysUntilDeadline <= 7) {
            deadlineClass = "upcoming";
        }

        li.className = `deadline-item ${deadlineClass}`;
        li.innerHTML = `
        <div class="deadline-header">
          <span class="deadline-title">${deadline.deadline_type}</span>
          <span class="deadline-date">${formatDate(
            deadline.deadline_date
        )}</span>
        </div>
        <div class="deadline-details">
          <span class="case-reference">Case #${deadline.case_id}</span>
          ${deadline.notes
                ? `<p class="deadline-notes">${deadline.notes}</p>`
                : ""
            }
        </div>
      `;
        ul.appendChild(li);
    });

    if (deadlines.length > 5) {
        const viewAll = document.createElement("a");
        viewAll.href = "/calendar";
        viewAll.className = "view-all-link";
        viewAll.textContent = "View all deadlines →";
        container.appendChild(viewAll);
    }

    container.appendChild(ul);
}

/**
 * Fetches only active cases for the overview
 */
function fetchActiveCasesOnly() {
    fetch("/api/get-all-cases-by-attorney")
        .then((res) => res.json())
        .then((cases) => {
            const openCases = cases.filter(
                (c) => c.case_status?.toLowerCase() === "open"
            );
            renderActiveCases(openCases);
        })
        .catch((err) => {
            console.error("Failed to load active cases", err);
            showError("active-cases", "Failed to load active cases");
        });
}

/**
 * Renders active cases in the overview
 * @param {Array} cases - Array of case objects
 */
function renderActiveCases(cases) {
    const container = document
        .getElementById("active-cases")
        .querySelector(".card-content");
    container.innerHTML = "";

    if (!cases || cases.length === 0) {
        container.innerHTML = `
        <div class="empty-state">
          <i class="fas fa-folder-open"></i>
          <p>No active cases</p>
        </div>
      `;
        return;
    }

    const ul = document.createElement("ul");
    ul.classList.add("active-cases-list");

    cases.slice(0, 5).forEach((caseItem) => {
        const li = document.createElement("li");
        li.className = "active-case-item";
        li.dataset.caseId = caseItem.case_id;
        li.innerHTML = `
        <div class="case-header">
          <span class="case-name">${caseItem.case_name || "Untitled Case"
            }</span>
          <span class="case-status ${caseItem.case_status.toLowerCase()}">${caseItem.case_status
            }</span>
        </div>
        <div class="case-details">
          <span class="case-type">${caseItem.case_type}</span>
          <span class="client-name">${caseItem.client_name}</span>
        </div>
      `;

        li.addEventListener("click", () => {
            window.location.href = `/case/${caseItem.case_id}/details`;
        });

        ul.appendChild(li);
    });

    if (cases.length > 5) {
        const viewAll = document.createElement("a");
        viewAll.href = "/cases";
        viewAll.className = "view-all-link";
        viewAll.textContent = "View all cases →";
        container.appendChild(viewAll);
    }

    container.appendChild(ul);
}

/**
 * Fetches and renders notifications
 */
function fetchAndRenderNotifications() {
    fetch("/api/notifications")
        .then((res) => res.json())
        .then((data) => {
            renderNotifications(data);
        })
        .catch((err) => {
            console.error("Error fetching notifications:", err);
            showError("notifications", "Failed to load notifications");
        });
}

/**
 * Renders notifications
 * @param {Array} notifications - Array of notification objects
 */
function renderNotifications(notifications) {
    const container = document
        .getElementById("notifications")
        .querySelector(".card-content");
    container.innerHTML = "";

    if (!notifications || notifications.length === 0) {
        container.innerHTML = `
        <div class="empty-state">
          <i class="fas fa-bell-slash"></i>
          <p>No new notifications</p>
        </div>
      `;
        return;
    }

    const ul = document.createElement("ul");
    ul.classList.add("notification-items");

    notifications.slice(0, 5).forEach((notification) => {
        const li = document.createElement("li");
        li.className = `notification-item ${notification.is_read ? "" : "unread"}`;
        li.dataset.id = notification.id;
        li.dataset.relatedId = notification.related_id;

        li.innerHTML = `
        <div class="notification-header">
          <span class="notification-type ${notification.type}">${notification.type
            }</span>
          <small class="notification-date">${formatDateTime(
                notification.created_at
            )}</small>
        </div>
        <p class="notification-message">${notification.message}</p>
      `;

        li.addEventListener("click", (e) => {
            if (notification.related_id) {
                window.location.href = `/case/${notification.related_id}/details`;
            }
            markNotificationAsRead(notification.id);
        });

        ul.appendChild(li);
    });

    if (notifications.length > 5) {
        const viewAll = document.createElement("a");
        viewAll.href = "/notifications";
        viewAll.className = "view-all-link";
        viewAll.textContent = "View all notifications →";
        container.appendChild(viewAll);
    }

    const markAllButton = document.createElement("button");
    markAllButton.textContent = "Mark All as Read";
    markAllButton.classList.add("mark-all-read");
    markAllButton.addEventListener("click", () => {
        markAllNotificationsAsRead();
    });

    container.appendChild(ul);
    container.appendChild(markAllButton);
}

/**
 * Marks a notification as read
 * @param {string} notificationId - ID of the notification to mark as read
 */
function markNotificationAsRead(notificationId) {
    fetch("/api/notifications/mark-read", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ notification_ids: [notificationId] }),
    }).then(() => {
        fetchAndRenderNotifications();
    });
}

/**
 * Marks all notifications as read
 */
function markAllNotificationsAsRead() {
    fetch("/api/notifications/mark-read", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ notification_ids: [] }),
    }).then(() => {
        fetchAndRenderNotifications();
    });
}

/**
 * Loads attorney document activity
 */
function loadAttorneyDocumentActivity() {
    fetch("/api/attorney/all-documents")
        .then((response) => {
            if (!response.ok) throw new Error("Failed to fetch documents");
            return response.json();
        })
        .then((documents) => {
            renderDocumentActivity(documents);
        })
        .catch((error) => {
            console.error("Error loading document activity:", error);
            showError("document-activity", "Failed to load documents");
        });
}

/**
 * Renders document activity
 * @param {Array} documents - Array of document objects
 */
function renderDocumentActivity(documents) {
    const container = document
        .getElementById("document-activity")
        .querySelector(".card-content");
    container.innerHTML = "";

    if (!documents || documents.length === 0) {
        container.innerHTML = `
        <div class="empty-state">
          <i class="fas fa-file-upload"></i>
          <p>No documents uploaded</p>
        </div>
      `;
        return;
    }

    // Sort by most recent first
    documents.sort((a, b) => new Date(b.uploaded_at) - new Date(a.uploaded_at));

    const ul = document.createElement("ul");
    ul.classList.add("document-list");

    documents.slice(0, 5).forEach((doc) => {
        const li = document.createElement("li");
        li.className = "document-item";
        li.innerHTML = `
        <div class="document-header">
          <span class="document-name">${truncate(doc.original_name, 30)}</span>
          <span class="document-date">${formatDate(doc.uploaded_at)}</span>
        </div>
        <div class="document-details">
          <span class="document-type">${doc.document_type}</span>
          <span class="document-case">Case #${doc.case_id}</span>
        </div>
      `;
        li.addEventListener("click", () => {
            const documentId = doc.id || doc.document_id;
            if (documentId) {
                window.location.href = `/document/${documentId}/details-page`;
            } else {
                console.warn("Missing document ID:", doc);
            }
        });
        ul.appendChild(li);
    });

    container.appendChild(ul);

    if (documents.length > 5) {
        const viewAll = document.createElement("a");
        viewAll.href = "/documents";
        viewAll.className = "view-all-link";
        viewAll.textContent = "View all documents →";
        container.appendChild(viewAll);
    }
}

/**
 * Sets up socket.io listeners
 * @param {Object} socket - Socket.io connection object
 */
function setupSocketListeners(socket) {
    socket.on("new_notification", (data) => {
        fetchAndRenderNotifications();
        showToast(data.message);
    });

    socket.on("case_update", (data) => {
        if (document.querySelector(".tab-content.active").id === "cases-tab") {
            fetchCases();
        }
        showToast(`Case ${data.case_id} updated: ${data.message}`);
    });

    socket.on("deadline_reminder", (data) => {
        if (document.querySelector(".tab-content.active").id === "overview-tab") {
            fetchDeadlines();
        }
        showToast(`Deadline reminder: ${data.message}`);
    });
}

/**
 * Shows a toast notification
 * @param {string} message - The message to display
 */
function showToast(message) {
    const toast = document.createElement("div");
    toast.className = "notification-toast";
    toast.innerHTML = `
      <i class="fas fa-info-circle"></i>
      <span>${message}</span>
    `;
    document.body.appendChild(toast);

    setTimeout(() => {
        toast.classList.add("fade-out");
        setTimeout(() => document.body.removeChild(toast), 300);
    }, 5000);
}

/**
 * Sets up event listeners for buttons
 */
function setupEventListeners() {
    // Add client button
    document.getElementById("add-client-btn")?.addEventListener("click", () => {
        window.location.href = "/add-client-form";
    });

    // Add case button
    document.getElementById("add-case-btn")?.addEventListener("click", () => {
        window.location.href = "/add_case";
    });

    // Add contact button
    document.getElementById("add-contact-btn")?.addEventListener("click", () => {
        window.location.href = "/add-contact-form";
    });

    // Refresh dashboard button
    document
        .getElementById("refresh-dashboard")
        ?.addEventListener("click", () => {
            fetchDashboardData();
            showToast("Dashboard refreshed");
        });
}

/**
 * Formats a date string
 * @param {string} dateString - The date string to format
 * @returns {string} Formatted date
 */
function formatDate(dateString) {
    if (!dateString) return "N/A";
    const options = { year: "numeric", month: "short", day: "numeric" };
    return new Date(dateString).toLocaleDateString(undefined, options);
}

/**
 * Formats a date/time string
 * @param {string} dateString - The date/time string to format
 * @returns {string} Formatted date/time
 */
function formatDateTime(dateString) {
    if (!dateString) return "N/A";
    const options = {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
}

/**
 * Truncates a string
 * @param {string} str - The string to truncate
 * @param {number} n - Maximum length
 * @returns {string} Truncated string
 */
function truncate(str, n) {
    return str.length > n ? str.substr(0, n - 1) + "..." : str;
}

/**
 * Shows an error message
 * @param {string} elementId - ID of the element to show error in
 * @param {string} message - Error message to display
 */
function showError(elementId, message) {
    const element =
        typeof elementId === "string"
            ? document.getElementById(elementId)
            : elementId;
    if (element) {
        element.innerHTML = `
        <div class="error-message">
          <i class="fas fa-exclamation-triangle"></i>
          <p>${message}</p>
        </div>
      `;
    }
}
/**
 * Formats phone number for display
 */
function formatPhoneNumber(phone) {
  if (!phone || phone === "N/A") return "N/A";
  
  // Simple formatting - you can enhance this as needed
  return phone.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
}
