// static/js/add_contact.js

document.addEventListener("DOMContentLoaded", function () {
  // Load cases for the dropdown
  loadCases();

  // Setup form submission
  setupFormSubmission();
});

/**
 * Load cases to populate the case dropdown
 */
async function loadCases() {
  try {
    const response = await fetch("/api/get-all-cases");
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    const caseDropdown = document.getElementById("case_link");

    // Clear existing options except the first one
    caseDropdown.innerHTML = '<option value="">Select a case</option>';

    if (data.cases && Array.isArray(data.cases)) {
      data.cases.forEach((caseItem) => {
        const option = document.createElement("option");
        option.value = caseItem.case_id;
        option.textContent = `${caseItem.case_name} (${caseItem.case_id})`;
        caseDropdown.appendChild(option);
      });
    }
  } catch (error) {
    console.error("Error loading cases:", error);
    showMessage("Error loading cases", "error");
  }
}

/**
 * Setup form submission handler
 */
function setupFormSubmission() {
  const form = document.getElementById("add-contact-form");

  // REPLACE THE EXISTING SUBMIT HANDLER WITH THIS NEW VERSION:
  form.addEventListener("submit", async function (e) {
    e.preventDefault();

    const formData = new FormData(form);
    const contactData = {
      name: formData.get("contact_name"),
      contact_type: formData.get("contact_type"),
      email: formData.get("email"),
      phone: formData.get("phone"),
      role: formData.get("role"),
      case_link: formData.get("case_link") || null,
      last_contact: formData.get("last_contact") || null,
    };

    // Validate required fields
    if (!contactData.name || !contactData.contact_type) {
      showMessage("Name and Contact Type are required", "error");
      return;
    }

    // Validate email if provided
    if (contactData.email && !validateEmail(contactData.email)) {
      showMessage("Please enter a valid email address", "error");
      return;
    }

    // Validate phone if provided
    if (contactData.phone && !validatePhone(contactData.phone)) {
      showMessage("Please enter a valid phone number", "error");
      return;
    }

    try {
      await createContact(contactData);
    } catch (error) {
      console.error("Error creating contact:", error);
      showMessage("Failed to create contact", "error");
    }
  });
}

/**
 * Create a new contact via API
 */
async function createContact(contactData) {
  try {
    const response = await fetch("/api/external-contacts", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(contactData),
    });

    const result = await response.json();

    if (response.ok) {
      showMessage("Contact created successfully!", "success");

      // Redirect after a short delay
      setTimeout(() => {
        window.location.href = "/attorney_dashboard";
      }, 1500);
    } else {
      showMessage(result.error || "Failed to create contact", "error");
    }
  } catch (error) {
    console.error("Network error:", error);
    showMessage("Network error occurred", "error");
    throw error;
  }
}

/**
 * Show message to user
 */
function showMessage(message, type) {
  // Remove existing messages
  const existingMessages = document.querySelectorAll(".message");
  existingMessages.forEach((msg) => msg.remove());

  // Create new message element
  const messageDiv = document.createElement("div");
  messageDiv.className = `message ${type}`;
  messageDiv.textContent = message;

  // Insert at top of form
  const form = document.getElementById("add-contact-form");
  form.parentNode.insertBefore(messageDiv, form);

  // Auto-remove after 5 seconds
  setTimeout(() => {
    messageDiv.remove();
  }, 5000);
}

/**
 * Validate email format
 */
function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate phone format
 */
function validatePhone(phone) {
  const phoneRegex = /^[\+]?[\d\s\-\(\)]+$/;
  return phoneRegex.test(phone);
}
