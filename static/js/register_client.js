document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('clientRegistrationForm');
    const messageContainer = document.getElementById('registration_message');

    // Toggle password visibility
    const passwordInput = document.getElementById('password');
    const togglePassword = document.querySelector('.toggle-password');
    if (togglePassword) {
        togglePassword.addEventListener('click', function() {
            const type = passwordInput.type === 'password' ? 'text' : 'password';
            passwordInput.type = type;
            this.textContent = type === 'password' ? '👁️' : '👁️‍🗨️';
        });
    }

    // Form submission handler
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const submitButton = form.querySelector('button[type="submit"]');
        submitButton.disabled = true;
        submitButton.textContent = 'Registering...';
        
        messageContainer.textContent = '';
        messageContainer.className = '';
        
        try {
            const formData = new FormData(form);
            console.log('DEBUG: Form data prepared');

            // Send as form data with AJAX header to ensure JSON response
            const response = await fetch(form.action, {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'  // Indicate this is an AJAX request
                },
                body: formData
            });

            console.log('DEBUG: Response received', response.status, response.statusText);

            // Handle redirect response (fallback)
            if (response.redirected) {
                console.log('DEBUG: Response was redirected to', response.url);
                window.location.href = response.url;
                return;
            }

            // Check if response is ok
            if (!response.ok) {
                console.error('DEBUG: Response not ok', response.status, response.statusText);
                const errorText = await response.text();
                console.error('DEBUG: Error response body:', errorText);
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            // Parse JSON response
            const result = await response.json();
            console.log('DEBUG: Parsed JSON result:', result);

            if (response.ok && result.status === 'success') {
                // Show success message briefly
                messageContainer.textContent = result.message || 'Registration successful!';
                messageContainer.className = 'success-message';

                // Navigate to dashboard after a short delay
                setTimeout(() => {
                    if (result.redirect) {
                        window.location.href = result.redirect;
                    } else {
                        // Fallback redirect
                        window.location.href = '/client_dashboard';
                    }
                }, 1000);
            } else {
                // Handle error response
                messageContainer.textContent = result.message || 'Registration failed';
                messageContainer.className = 'error-message';

                // Handle field-specific errors
                if (result.field) {
                    showFieldError(result.field, result.message);
                }
            }
        } catch (error) {
            console.error('Registration error:', error);
            messageContainer.textContent = 'An unexpected error occurred. Please try again.';
            messageContainer.className = 'error-message';
        } finally {
            submitButton.disabled = false;
            submitButton.textContent = 'Register';
        }
    });

    // Real-time validation
    const emailInput = document.getElementById('email');
    if (emailInput) {
        emailInput.addEventListener('blur', function() {
            const email = this.value;
            if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                showFieldError('email', 'Please enter a valid email address');
            }
        });
    }

    function showFieldError(field, message) {
        const input = document.querySelector(`[name="${field}"]`);
        if (!input) return;
        
        let errorElement = input.nextElementSibling;
        if (!errorElement || !errorElement.classList.contains('field-error')) {
            errorElement = document.createElement('div');
            errorElement.className = 'field-error';
            input.parentNode.insertBefore(errorElement, input.nextSibling);
        }
        
        errorElement.textContent = message;
        errorElement.style.color = 'red';
        errorElement.style.fontSize = '0.8em';
        errorElement.style.marginTop = '5px';
        input.style.borderColor = 'red';
    }
});