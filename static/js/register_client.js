document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('clientRegistrationForm');
    const messageContainer = document.getElementById('registration_message');

    // Toggle password visibility
    const passwordInput = document.getElementById('password');
    const togglePassword = document.querySelector('.toggle-password');
    if (togglePassword) {
        togglePassword.addEventListener('click', function() {
            const type = passwordInput.type === 'password' ? 'text' : 'password';
            passwordInput.type = type;
            this.textContent = type === 'password' ? '👁️' : '👁️‍🗨️';
        });
    }

    // Form submission handler
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const submitButton = form.querySelector('button[type="submit"]');
        submitButton.disabled = true;
        submitButton.textContent = 'Registering...';
        
        messageContainer.textContent = '';
        messageContainer.className = '';
        
        try {
            const formData = new FormData(form);

            // Send as form data with AJAX header to ensure JSON response
            const response = await fetch(form.action, {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'  // Indicate this is an AJAX request
                },
                body: formData
            });

            // Handle redirect response (fallback)
            if (response.redirected) {
                window.location.href = response.url;
                return;
            }

            // Parse JSON response
            const result = await response.json();

            if (response.ok && result.status === 'success') {
                // Show success message briefly
                messageContainer.textContent = result.message || 'Registration successful!';
                messageContainer.className = 'success-message';

                // Navigate to dashboard after a short delay
                setTimeout(() => {
                    if (result.redirect) {
                        window.location.href = result.redirect;
                    } else {
                        // Fallback redirect
                        window.location.href = '/client_dashboard';
                    }
                }, 1000);
            } else {
                // Handle error response
                messageContainer.textContent = result.message || 'Registration failed';
                messageContainer.className = 'error-message';

                // Handle field-specific errors
                if (result.field) {
                    showFieldError(result.field, result.message);
                }
            }
        } catch (error) {
            console.error('Registration error:', error);
            messageContainer.textContent = 'An unexpected error occurred. Please try again.';
            messageContainer.className = 'error-message';
        } finally {
            submitButton.disabled = false;
            submitButton.textContent = 'Register';
        }
    });

    // Real-time validation
    const emailInput = document.getElementById('email');
    if (emailInput) {
        emailInput.addEventListener('blur', function() {
            const email = this.value;
            if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                showFieldError('email', 'Please enter a valid email address');
            }
        });
    }

    function showFieldError(field, message) {
        const input = document.querySelector(`[name="${field}"]`);
        if (!input) return;
        
        let errorElement = input.nextElementSibling;
        if (!errorElement || !errorElement.classList.contains('field-error')) {
            errorElement = document.createElement('div');
            errorElement.className = 'field-error';
            input.parentNode.insertBefore(errorElement, input.nextSibling);
        }
        
        errorElement.textContent = message;
        errorElement.style.color = 'red';
        errorElement.style.fontSize = '0.8em';
        errorElement.style.marginTop = '5px';
        input.style.borderColor = 'red';
    }
});