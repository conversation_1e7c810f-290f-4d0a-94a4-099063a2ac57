from case_management.extensions import db
from case_management.models import Client, Attorney
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session
import re

class ClientRepository:
    
    @staticmethod
    def get_all_clients():
        """Retrieve all clients"""
        return Client.query.all()
    
    @staticmethod
    def get_clients_by_primary_attorney(attorney_id):
        """Fetch clients where the attorney is the primary attorney."""
        return Client.query.filter_by(primary_attorney_id=attorney_id).all()

    @staticmethod
    def get_clients_by_collaborating_attorney(attorney_id):
        """Fetch clients where the attorney is a collaborator."""
        return Client.query.join(
            Client.collaborating_attorneys
        ).filter(
            Attorney.attorney_id == attorney_id
        ).all()
    
    @staticmethod
    def get_clients_by_law_firm(law_firm_id):
        print("Session bound to DB:", db.session.bind)

        """
        Fetches all clients associated with a specific law firm.

        Args:
            law_firm_id (int): The ID of the law firm.

        Returns:
            list: A list of Client objects associated with the law firm.
        """
        try:
            clients = db.session.query(Client).filter_by(law_firm_id=law_firm_id).all()
            return clients
        except Exception as e:
            print(f"An error occurred while fetching clients: {str(e)}")
            raise e

    
    @staticmethod
    def get_clients_by_attorney_id(attorney_id):
        return Client.query.filter_by(attorney_id=attorney_id).order_by(Client.name).all()

    
    @staticmethod
    def get_client_by_name(name):
        """Retrieve a client by name."""
        return Client.query.filter_by(name=name).first()
    
    @staticmethod
    def search_clients_by_name(query):
        """Search for clients whose name contains the query string (case-insensitive)."""
        return Client.query.filter(Client.name.ilike(f"%{query}%")).all()
    
    @staticmethod
    def create_client(
        client_id,
        name,
        username,
        password_hash,
        email,
        phone_number=None,
        address=None,
        description=None,
        law_firm_id=None,
        primary_attorney_id=None,
        collaborating_attorneys=None,
        custom_fields=None
    ):
        try:
            # Validate required fields (only check non-optional fields)
            if not all([client_id, name, username, password_hash, email]):
                raise ValueError("Missing required fields: client_id, name, username, password_hash, or email")

            print("Client creation")
            # Create a new Client instance
            new_client = Client(
                client_id=client_id,
                name=name,
                username=username,
                password_hash=password_hash,
                email=email,  #  This triggers `validate_email()`
                phone_number=phone_number,  #  This triggers `validate_phone_number()`
                address=address,  # 🚀 This triggers `validate_address()`
                description=description,
                law_firm_id=law_firm_id,
                primary_attorney_id=primary_attorney_id,
                custom_fields=custom_fields or {}  # Default to empty dictionary if None
            )

            # Add collaborating attorneys (many-to-many relationship)
            if collaborating_attorneys:
                for attorney_id in collaborating_attorneys:
                    attorney = Attorney.query.filter_by(attorney_id=attorney_id).first()
                    if attorney:
                        new_client.collaborating_attorneys.append(attorney)

            # Add the new client to the database
            db.session.add(new_client)
            db.session.commit()

            return new_client

        except ValueError as e:  #  Catch validation errors and return messages
            db.session.rollback()  #  Rollback to prevent partial insert
            print(f"Validation error: {e}")
            return None

        except Exception as e:  # Catch unexpected errors
            db.session.rollback()
            print(f"Unexpected error during client creation: {e}")
            return None
    
    @staticmethod
    def get_client_by_email(email):
        """
        Retrieve a client by their email address.
        """
        return Client.query.filter_by(email=email).first()
    
        
    @staticmethod
    def get_client_by_id(client_id):
        """Retrieves a client by their unique client_id."""
        return Client.query.filter_by(client_id=client_id).first()

    @staticmethod
    def get_client_by_username(username):
        """Retrieve a client by their username."""
        client = Client.query.filter_by(username=username).first()
        if client:
            print(f"Client Name (from DB): {client.name}")
        return client

    @staticmethod
    def update_client(client_id, name=None, username=None, password_hash=None, email=None, description=None, phone_number=None, address=None, custom_fields=None, primary_attorney_id=None, collaborating_attorneys_ids=None):
        """
        Updates client information, including primary attorney and collaborating attorneys.
        :param client_id: The ID of the client to update.
        :param name: Updated name (optional).
        :param username: Updated username (optional).
        :param password_hash: Updated password hash (optional).
        :param email: Updated email (optional).
        :param description: Updated description (optional).
        :param phone_number: Updated phone number (optional).
        :param address: Updated address (optional).
        :param custom_fields: Updated custom fields (optional, JSON-serializable).
        :param primary_attorney_id: Updated primary attorney ID (optional).
        :param collaborating_attorneys_ids: List of updated collaborating attorney IDs (optional).
        :return: The updated client object or None if the update fails.
        """
        try:
            # Fetch the client by ID
            client = ClientRepository.get_client_by_id(client_id)
            if not client:
                return None  # Client not found

            # Update basic fields
            if name:
                client.name = name
            if username:
                client.username = username
            if password_hash:
                client.password_hash = password_hash
            if email:
                if not ClientRepository.is_valid_email(email):
                    raise ValueError("Invalid email format")
                client.email = email
            if description is not None:
                client.description = description
            if phone_number is not None:
                client.phone_number = phone_number
            if address is not None:
                client.address = address

            # Update custom_fields
            if custom_fields is not None:
                # Merge existing custom_fields with new custom_fields
                if client.custom_fields:
                    client.custom_fields = {**client.custom_fields, **custom_fields}
                else:
                    client.custom_fields = custom_fields

            # Update primary attorney
            if primary_attorney_id is not None:
                client.primary_attorney_id = primary_attorney_id

            # Update collaborating attorneys (many-to-many relationship)
            if collaborating_attorneys_ids is not None:
                # Validate that all collaborating attorney IDs exist
                valid_collaborating_attorneys = [
                    Attorney.query.filter_by(attorney_id=attorney_id).first()
                    for attorney_id in collaborating_attorneys_ids
                    if Attorney.query.filter_by(attorney_id=attorney_id).first()
                ]
                client.collaborating_attorneys = valid_collaborating_attorneys

            # Commit changes to the database
            db.session.commit()
            return client

        except SQLAlchemyError as e:
            # Rollback in case of database error
            db.session.rollback()
            print(f"Database error: {e}")
            return None
        except ValueError as ve:
            # Handle invalid input errors
            print(f"Validation error: {ve}")
            return None
        
    @staticmethod
    def delete_client_by_name(client_name):
        """Deletes a client from the database by name."""
        try:
            client = ClientRepository.get_client_by_name(client_name)
            if client:
                db.session.delete(client)
                db.session.commit()
                return True
            return False
        except SQLAlchemyError as e:
            db.session.rollback()
            print(f"Database error: {e}")
            return False

    @staticmethod
    def delete_client_by_id(client_id):
        """Deletes a client from the database by client_id."""
        try:
            client = ClientRepository.get_client_by_id(client_id)
            if client:
                db.session.delete(client)
                db.session.commit()
                return True
            return False
        except Exception as e:
            db.session.rollback()
            print(f"Database error: {e}")
            return False

    @staticmethod
    def is_valid_email(email):
        """Validates email format using regex."""
        email_regex = r'^[\w\.-]+@[\w\.-]+\.\w+$'
        return re.match(email_regex, email) is not None
    
    
