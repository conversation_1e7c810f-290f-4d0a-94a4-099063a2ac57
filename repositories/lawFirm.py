from case_management.models import LawFirm 
from case_management.extensions import db

class LawFirmRepository:
    
    @staticmethod
    def get_all_law_firms():
        return LawFirm.query.all()

    @staticmethod
    def get_law_firm_by_id(law_firm_id):
        return LawFirm.query.filter_by(id=law_firm_id).first()
    

    @staticmethod
    def get_law_firm_by_name(name):
        """
        Get law firm by name with case-insensitive search.
        First tries exact match, then case-insensitive match.
        """
        if not name:
            return None

        # First try exact match
        exact_match = LawFirm.query.filter_by(name=name).first()
        if exact_match:
            return exact_match

        # Then try case-insensitive match
        case_insensitive_match = LawFirm.query.filter(
            LawFirm.name.ilike(name)
        ).first()
        if case_insensitive_match:
            return case_insensitive_match

        # Finally try partial match (contains the search term)
        partial_match = LawFirm.query.filter(
            LawFirm.name.ilike(f'%{name}%')
        ).first()
        return partial_match

    @staticmethod
    def search_law_firms_by_name(name):
        """
        Search for law firms by name with fuzzy matching.
        Returns a list of potential matches.
        """
        if not name:
            return []

        # Search for firms that contain the search term (case-insensitive)
        matches = LawFirm.query.filter(
            LawFirm.name.ilike(f'%{name}%')
        ).all()

        return matches

    @staticmethod
    def create_law_firm(name, address=None, contact_email=None, phone_number=None):
       
        try:
            # Validate required fields
            if not name:
                raise ValueError("Name is required to create a law firm.")

            # Create a new LawFirm instance
            new_law_firm = LawFirm(
                name=name,
                address=address,
                contact_email=contact_email,
                phone_number=phone_number
            )

            # Add the new law firm to the database
            db.session.add(new_law_firm)
            db.session.commit()

            return new_law_firm

        except ValueError as e:
            db.session.rollback()
            print(f"Validation error: {e}")
            return None

        except Exception as e:
            db.session.rollback()
            print(f"Unexpected error during law firm creation: {e}")
            return None
    
    @staticmethod
    def get_or_create_law_firm(name: str, address: str = None, contact_email: str = None, phone_number: str = None):
        law_firm = LawFirm.query.filter_by(name=name).first()
        if not law_firm:
            law_firm = LawFirm(
                name=name,
                address=address,
                contact_email=contact_email,
                phone_number=phone_number
            )
            db.session.add(law_firm)
            db.session.commit()
            db.session.refresh(law_firm)
        return law_firm
    
    
    @staticmethod
    def update_law_firm(law_firm_id, name=None, address=None, contact_email=None, phone_number=None):
       
        try:
            # Fetch the law firm by ID
            law_firm = LawFirm.query.filter_by(id=law_firm_id).first()
            if not law_firm:
                return False  # Law firm not found

            # Update fields if provided
            if name:
                law_firm.name = name
            if address:
                law_firm.address = address
            if contact_email:
                law_firm.contact_email = contact_email
            if phone_number is not None:
                law_firm.phone_number = phone_number

            # Save changes to the database
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error updating law firm: {e}")
            return False