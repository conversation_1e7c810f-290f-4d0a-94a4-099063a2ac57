from case_management.models import Notification
from case_management.extensions import db


class NotificationRepository:
    @staticmethod
    def get_unread_notifications(user_id, user_role):
        return db.session.query(Notification).filter(
            Notification.user_id == user_id,
            Notification.user_type == user_role,
            Notification.is_read == False
        ).order_by(Notification.created_at.desc()).all()

    @staticmethod
    def mark_notifications_read(user_id, user_role, notification_ids=None):
        query = db.session.query(Notification).filter(
            Notification.user_id == user_id,
            Notification.user_type == user_role
        )

        if notification_ids:
            query = query.filter(Notification.id.in_(notification_ids))
        else:
            query = query.filter(Notification.is_read == False)

        query.update({"is_read": True}, synchronize_session=False)
        db.session.commit()

    @staticmethod
    def create_notification(user_id, user_type, message, notification_type, related_id=None):
        """Create and emit a notification"""
        notification = Notification(
            user_id=user_id,
            user_type=user_type,
            message=message,
            notification_type=notification_type,
            related_id=related_id
        )
        db.session.add(notification)
        db.session.commit()

        NotificationRepository.emit_notification(user_id, user_type, notification)

    @staticmethod
    def emit_notification(user_id, user_type, notification):
        """Emit real-time notification via Socket.IO"""
        from case_management.extensions import socketio  # Import here to avoid circular imports
        
        room = f"{user_type}_{user_id}"
        from datetime import datetime

        def safe_iso(dt):
            if isinstance(dt, datetime):
                return dt.isoformat()
            return str(dt)

        socketio.emit('new_notification', {
            'id': notification.id,
            'message': notification.message,
            'type': notification.notification_type,
            'related_id': notification.related_id,
            'created_at': safe_iso(notification.created_at)
        }, room=room)
