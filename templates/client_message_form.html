<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Client Messaging</title>
    <style>
        /* Base Styles */
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            color: #333;
        }
        
        h2, h3 {
            color: #2c3e50;
            margin-bottom: 20px;
        }
        
        /* Form Styles */
        label {
            display: block;
            margin: 15px 0 5px;
            font-weight: bold;
        }
        
        /* Dropdown Styling - Fixed alignment */
        select {
            width: 100%;
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 20px;
            background-color: white;
            cursor: pointer;
        }
        
        /* Textarea Styling */
        textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            min-height: 120px;
            resize: vertical;
            margin-bottom: 15px;
        }
        
        /* Button Styling */
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #2980b9;
        }
        
        button:disabled {
            background-color: #95a5a6;
            cursor: not-allowed;
        }
        
        /* Message Feedback */
        .message-feedback {
            margin: 15px 0;
            padding: 10px;
            border-radius: 4px;
        }
        
        .success-message {
            background-color: #dff0d8;
            color: #3c763d;
        }
        
        .error-message {
            background-color: #f2dede;
            color: #a94442;
        }
        
        /* Message History */
        #messageHistory {
            margin-top: 20px;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }
        
        #messageHistory p {
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f5f5f5;
        }
        
        /* Loading State */
        .loading {
            position: relative;
            pointer-events: none;
        }
        
        .loading::after {
            content: "";
            position: absolute;
            top: 50%;
            right: 10px;
            transform: translateY(-50%);
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: translateY(-50%) rotate(0deg); }
            100% { transform: translateY(-50%) rotate(360deg); }
        }
    </style>
    <script src="{{ url_for('static', filename='js/client_messaging.js') }}" defer></script>
</head>
<body>
    <h2>Send a Message</h2>

    <!-- Case Selection Dropdown -->
    <label for="caseDropdown">Select Case:</label>
    <select id="caseDropdown">
        <option value="" disabled selected>Loading cases...</option>
    </select>

    <!-- Message Input -->
    <label for="messageInput">Enter your message:</label>
    <textarea id="messageInput" rows="4" cols="50" placeholder="Type your message here..."></textarea>

    <!-- Send Button -->
    <button id="sendMessageButton">Send Message</button>

    <div id="message-container" class="message-feedback"></div>

    <!-- Previous Messages -->
    <h3>Previous Messages</h3>
    <div id="messageHistory"></div>

    {% include "partials/posthog.html" %}
</body>
</html>