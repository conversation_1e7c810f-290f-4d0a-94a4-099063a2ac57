<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add New Contact | Law Firm CRM</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --light-gray: #f8f9fa;
            --medium-gray: #e9ecef;
            --dark-gray: #6c757d;
            --success-color: #28a745;
            --error-color: #dc3545;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background-color: var(--light-gray);
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2.5rem;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }
        
        .form-header {
            text-align: center;
            margin-bottom: 2.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--medium-gray);
        }
        
        .form-header h1 {
            color: var(--primary-color);
            font-size: 1.8rem;
            margin-bottom: 0.5rem;
        }
        
        .form-header p {
            color: var(--dark-gray);
            font-size: 0.95rem;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group.full-width {
            grid-column: span 2;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--primary-color);
            font-size: 0.9rem;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.8rem 1rem;
            border: 1px solid var(--medium-gray);
            border-radius: 6px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
        }
        
        .form-group input::placeholder {
            color: var(--dark-gray);
            opacity: 0.6;
        }
        
        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid var(--medium-gray);
        }
        
        .btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.95rem;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #2980b9;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background-color: var(--medium-gray);
            color: var(--primary-color);
        }
        
        .btn-secondary:hover {
            background-color: #d6d8db;
        }
        
        .message {
            padding: 1rem;
            margin-bottom: 1.5rem;
            border-radius: 6px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .message.success {
            background-color: rgba(40, 167, 69, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(40, 167, 69, 0.2);
        }
        
        .message.error {
            background-color: rgba(220, 53, 69, 0.1);
            color: var(--error-color);
            border: 1px solid rgba(220, 53, 69, 0.2);
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 1.5rem;
                margin: 1rem;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .form-group.full-width {
                grid-column: span 1;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="form-header">
            <h1><i class="fas fa-user-plus"></i> Add New Contact</h1>
            <p>Fill out the form below to add a new contact to your network</p>
        </div>

        <form id="add-contact-form">
            <div class="form-grid">
                <div class="form-group">
                    <label for="contact_name"><i class="fas fa-user"></i> Contact Name</label>
                    <input
                        type="text"
                        id="contact_name"
                        name="contact_name"
                        required
                        placeholder="John Doe"
                    />
                </div>

                <div class="form-group">
                    <label for="contact_type"><i class="fas fa-tag"></i> Contact Type</label>
                    <select id="contact_type" name="contact_type" required>
                        <option value="">Select Contact Type</option>
                        <option value="External">External</option>
                        <option value="Witness">Witness</option>
                        <option value="Expert">Expert</option>
                        <option value="Vendor">Vendor</option>
                        <option value="Other">Other</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="email"><i class="fas fa-envelope"></i> Email</label>
                    <input
                        type="email"
                        id="email"
                        name="email"
                        placeholder="<EMAIL>"
                    />
                </div>

                <div class="form-group">
                    <label for="phone"><i class="fas fa-phone"></i> Phone</label>
                    <input
                        type="tel"
                        id="phone"
                        name="phone"
                        placeholder="(*************"
                    />
                </div>

                <div class="form-group">
                    <label for="role"><i class="fas fa-briefcase"></i> Role</label>
                    <select id="role" name="role">
                        <option value="">Select Role</option>
                        <option value="Witness">Witness</option>
                        <option value="Opposing Counsel">Opposing Counsel</option>
                        <option value="Expert Witness">Expert Witness</option>
                        <option value="Interpreter">Interpreter</option>
                        <option value="Court Reporter">Court Reporter</option>
                        <option value="Investigator">Investigator</option>
                        <option value="Process Server">Process Server</option>
                        <option value="Judge">Judge</option>
                        <option value="Other">Other</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="case_link"><i class="fas fa-link"></i> Link to Case (optional)</label>
                    <select id="case_link" name="case_link">
                        <option value="">Select a case</option>
                        <!-- Cases will be populated via JavaScript -->
                    </select>
                </div>

                <div class="form-group full-width">
                    <label for="last_contact"><i class="fas fa-calendar-alt"></i> Last Contact Date</label>
                    <input type="datetime-local" id="last_contact" name="last_contact" />
                </div>
            </div>

            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="window.history.back()">
                    <i class="fas fa-times"></i> Cancel
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Save Contact
                </button>
            </div>
        </form>
    </div>

    <script src="{{ url_for('static', filename='js/add_contact.js') }}"></script>
</body>
</html>