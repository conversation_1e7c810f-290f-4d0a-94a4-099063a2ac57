{% extends "base.html" %}

{% block content %}
<div class="support-container">
    <h1>Support Center</h1>
    
    <div class="support-tabs">
        <button class="tab-btn active" data-tab="new-ticket">New Ticket</button>
        <button class="tab-btn" data-tab="ticket-history">Ticket History</button>
    </div>
    
    <div id="new-ticket" class="tab-content active">
        <div id="feedback-modal" class="modal">
            <div class="modal-content">
                <h3>Help Us Improve</h3>
                <p>Before submitting a ticket, please share your feedback about our system.</p>
                
                <form id="pre-feedback-form">
                    <div class="rating-input">
                        <label>Rating (1-5 stars):</label>
                        <div class="stars">
                            {% for i in range(5, 0, -1) %}
                                <input type="radio" id="star-{{i}}" name="rating" value="{{i}}">
                                <label for="star-{{i}}">★</label>
                            {% endfor %}
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="feedback-comment">Comments (required):</label>
                        <textarea id="feedback-comment" name="comment" required></textarea>
                    </div>
                    
                    <button type="submit" class="btn-primary">Submit Feedback</button>
                </form>
            </div>
        </div>
        
        <form id="ticket-form" style="display:none;">
            <h3>Submit New Support Ticket</h3>
            
            <div class="form-group">
                <label>Name:</label>
                <input type="text" id="ticket-name" readonly>
            </div>
            
            <div class="form-group">
                <label>Email:</label>
                <input type="email" id="ticket-email" readonly>
            </div>
            
            <div class="form-group">
                <label>Firm Name:</label>
                <input type="text" id="ticket-firm" >
            </div>
            
            <div class="form-group">
                <label>User Type:</label>
                <input type="text" id="ticket-user-type" readonly>
            </div>
            
            <div class="form-group">
                <label for="ticket-priority">Priority:</label>
                <select id="ticket-priority" required>
                    <option value="">Select priority</option>
                    <option value="Low">Low</option>
                    <option value="Medium">Medium</option>
                    <option value="High">High</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="ticket-description">Issue Description:</label>
                <textarea id="ticket-description" required></textarea>
            </div>
            
            <button type="submit" class="btn-primary">Submit Ticket</button>
        </form>
    </div>
    
    <div id="ticket-history" class="tab-content">
        <div class="filter-controls">
            <label>Filter by Status:</label>
            <select id="status-filter">
                <option value="all">All</option>
                <option value="Open">Open</option>
                <option value="In Progress">In Progress</option>
                <option value="Resolved">Resolved</option>
            </select>
        </div>
        
        <div id="tickets-list">
            <!-- Tickets will be loaded here -->
        </div>
    </div>
</div>

<!-- Feedback after resolution modal -->
<div id="resolution-feedback-modal" class="modal" style="display:none;">
    <div class="modal-content">
        <h3>How was your support experience?</h3>
        <p>Please rate your experience with resolving this ticket.</p>
        
        <form id="post-feedback-form">
            <input type="hidden" id="feedback-ticket-id">
            
            <div class="rating-input">
                <label>Rating (1-5 stars):</label>
                <div class="stars">
                    {% for i in range(5, 0, -1) %}
                        <input type="radio" id="post-star-{{i}}" name="rating" value="{{i}}">
                        <label for="post-star-{{i}}">★</label>
                    {% endfor %}
                </div>
            </div>
            
            <div class="form-group">
                <label for="post-feedback-comment">Comments (required):</label>
                <textarea id="post-feedback-comment" name="comment" required></textarea>
            </div>
            
            <button type="submit" class="btn-primary">Submit Feedback</button>
        </form>
    </div>
</div>

<link rel="stylesheet" href="/static/css/support.css">
<script src="/static/js/support.js"></script>
{% endblock %}