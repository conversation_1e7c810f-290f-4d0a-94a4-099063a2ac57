<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{% block title %}Case Management App{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- Font Awesome for icons -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
    <script>
      !(function (t, e) {
        var o, n, p, r;
        e.__SV ||
          ((window.posthog = e),
          (e._i = []),
          (e.init = function (i, s, a) {
            function g(t, e) {
              var o = e.split(".");
              2 == o.length && ((t = t[o[0]]), (e = o[1])),
                (t[e] = function () {
                  t.push([e].concat(Array.prototype.slice.call(arguments, 0)));
                });
            }
            ((p = t.createElement("script")).type = "text/javascript"),
              (p.crossOrigin = "anonymous"),
              (p.async = !0),
              (p.src =
                s.api_host.replace(".i.posthog.com", "-assets.i.posthog.com") +
                "/static/array.js"),
              (r = t.getElementsByTagName("script")[0]).parentNode.insertBefore(
                p,
                r
              );
            var u = e;
            for (
              void 0 !== a ? (u = e[a] = []) : (a = "posthog"),
                u.people = u.people || [],
                u.toString = function (t) {
                  var e = "posthog";
                  return (
                    "posthog" !== a && (e += "." + a), t || (e += " (stub)"), e
                  );
                },
                u.people.toString = function () {
                  return u.toString(1) + ".people (stub)";
                },
                o =
                  "init me ws ys ps bs capture je Di ks register register_once register_for_session unregister unregister_for_session Ps getFeatureFlag getFeatureFlagPayload isFeatureEnabled reloadFeatureFlags updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures on onFeatureFlags onSurveysLoaded onSessionId getSurveys getActiveMatchingSurveys renderSurvey canRenderSurvey canRenderSurveyAsync identify setPersonProperties group resetGroups setPersonPropertiesForFlags resetPersonPropertiesForFlags setGroupPropertiesForFlags resetGroupPropertiesForFlags reset get_distinct_id getGroups get_session_id get_session_replay_url alias set_config startSessionRecording stopSessionRecording sessionRecordingStarted captureException loadToolbar get_property getSessionProperty Es $s createPersonProfile Is opt_in_capturing opt_out_capturing has_opted_in_capturing has_opted_out_capturing clear_opt_in_out_capturing Ss debug xs getPageViewId captureTraceFeedback captureTraceMetric".split(
                    " "
                  ),
                n = 0;
              n < o.length;
              n++
            )
              g(u, o[n]);
            e._i.push([i, s, a]);
          }),
          (e.__SV = 1));
      })(document, window.posthog || []);
      posthog.init("phc_Bepvw8uWTlyd2faPRvU1FbT0gxLnpmnYlWW0frIbie2", {
        api_host: "https://us.i.posthog.com",
        person_profiles: "identified_only",
        autocapture: true, // Tracks clicks, form inputs, etc.
        session_recording: {
          recordCanvas: true, // optional: captures canvas interactions
          maskAllText: false, // optional: for privacy if needed
        },
      });

      // Track page views for key routes
      const pathname = window.location.pathname;
      const trackedPages = [
        "/calendar",
        "/task_management",
        "/dashboard",
        "/attorney_dashboard",
      ];
      if (trackedPages.includes(pathname)) {
        posthog.capture("page_visited", { path: pathname });
      }
    </script>

    <style>
      .navbar {
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .nav-link {
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .dropdown-menu {
        border: none;
        box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
      }

      @media (max-width: 991.98px) {
        .navbar-collapse {
          padding: 1rem 0;
        }

        .nav-item {
          margin: 0.25rem 0;
        }
      }
    </style>
  </head>

  <body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
      <div class="container">
        <a class="navbar-brand fw-bold nav-brand" href="#">
          <i class="fas fa-balance-scale"></i>

          <span class="text-primary">LawFirm </span>Pro
        </a>
        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarContent"
          aria-controls="navbarContent"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarContent">
          <ul class="navbar-nav me-auto mb-2 mb-lg-0">
            <li class="nav-item">
              <a class="nav-link active" href="/attorney_dashboard"
                ><i class="fas fa-home"></i>Home</a
              >
            </li>
            <li class="nav-item">
              <a class="nav-link" href="/calendar"
                ><i class="fas fa-calendar-alt"></i>Calendar</a
              >
            </li>
            <li class="nav-item">
              <a class="nav-link" href="/task_management"
                ><i class="fas fa-tasks"></i>Tasks</a
              >
            </li>
            <li class="nav-item">
              <a class="nav-link" href="/documents"
                ><i class="fas fa-file-alt"></i>Documents</a
              >
            </li>
            <li class="nav-item">
              <a class="nav-link" href="/dashboard"
                ><i class="fas fa-folder-open"></i>Cases</a
              >
            </li>
            <li class="nav-item">
              <a class="nav-link" href="/support"
                ><i class="fas fa-folder-open"></i>Support</a
              >
            </li>
          </ul>
          <ul class="navbar-nav">
            <li>{%include 'partials/notification.html' %}</li>
            <li class="nav-item dropdown">
              <a
                class="nav-link dropdown-toggle"
                href="#"
                id="moreDropdown"
                role="button"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <i class="fas fa-ellipsis-h"></i>More
              </a>
              <ul
                class="dropdown-menu dropdown-menu-end"
                aria-labelledby="moreDropdown"
              >
                <li>
                  <a class="dropdown-item" href="/attorney_dashboard"
                    ><i class="fas fa-user me-2"></i>Profile</a
                  >
                </li>
                <li>
                  <hr class="dropdown-divider" />
                </li>
                <li>
                  <a class="dropdown-item" href="/logout"
                    ><i class="fas fa-sign-out-alt me-2"></i>Logout</a
                  >
                </li>
              </ul>
            </li>
          </ul>
        </div>
      </div>
    </nav>

    <!-- Page-specific content goes here -->
    <div class="container my-4">{% block content %} {% endblock %}</div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
      // Track "Create Task" button clicks
      document.addEventListener("DOMContentLoaded", function () {
        const createTaskBtn = document.querySelector("#createTaskBtn");
        if (createTaskBtn) {
          createTaskBtn.addEventListener("click", function () {
            posthog.capture("create_task_clicked");
          });
        }
        const editTaskBtn = document.querySelector(".btn-edit");
        if (editTaskBtn) {
          editTaskBtn.addEventListener("click", function () {
            posthog.capture("edit_task_clicked");
          });
        }
        const deleteTaskBtn = document.querySelector(".btn-delete");
        if (deleteTaskBtn) {
          deleteTaskBtn.addEventListener("click", function () {
            posthog.capture("delete_task_clicked");
          });
        }
        // Track time spent on page
        let pageStart = Date.now();
        window.addEventListener("beforeunload", function () {
          const timeSpent = Math.round((Date.now() - pageStart) / 1000);
          posthog.capture("time_spent_on_page", {
            path: window.location.pathname,
            seconds: timeSpent,
          });
        });

        // Track scroll depth
        window.addEventListener(
          "scroll",
          function () {
            const scrollDepth = Math.round(
              (window.scrollY / document.body.scrollHeight) * 100
            );
            posthog.capture("scroll_depth", {
              path: window.location.pathname,
              percent: scrollDepth,
            });
          },
          { passive: true }
        );
      });
    </script>
  </body>
</html>
