<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}" type="image/x-icon" />
    <title>Login - Advanced Legal Tech Case Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .branding {
            text-align: center;
            margin-top: 1.5rem;
            margin-bottom: 1.5rem;
            padding: 0 1rem;
        }

        .branding h1 {
            font-size: clamp(1.5rem, 4vw, 2.5rem);
            font-weight: 700;
            color: #fff;
            letter-spacing: 1px;
            margin-bottom: 0.5rem;
        }

        .branding p {
            color: #e0e0e0;
            font-size: clamp(0.9rem, 2vw, 1.2rem);
            margin-bottom: 0;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin: 0 auto 1.5rem auto;
            max-width: 450px;
            width: 95%;
        }

        .header-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.2rem 1rem;
            text-align: center;
        }

        .header-section h1 {
            font-size: clamp(1.3rem, 3vw, 1.8rem);
        }

        .form-section {
            padding: 1.5rem;
        }

        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        #emailLoginForm {
            transition: all 0.3s ease;
        }

        .alert {
            margin-bottom: 1.5rem;
        }

      

        .social-btn {
            width: 100%;
            border-radius: 25px;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 12px;
        }

        .btn-email {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #fff;
            border: none;
        }

        .btn-email:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            color: #fff;
        }

        .register-card {
            background: #f8f9fa;
            border-radius: 15px;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.08);
            padding: 1.2rem 1rem;
            margin: 1.5rem auto 0 auto;
            text-align: center;
            max-width: 450px;
            width: 95%;
        }

        .register-card h2 {
            font-size: clamp(1.1rem, 2.5vw, 1.3rem);
            color: #667eea;
            margin-bottom: 0.5rem;
        }

        .register-inline {
            display: flex;
            gap: 0.5rem;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
        }

        /* Custom select styling with arrow */
        .custom-select {
            position: relative;
        }

        .custom-select select {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            padding-right: 2.5rem;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23667eea' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 0.75rem center;
            background-size: 16px 12px;
        }

        /* Password input group styling - UPDATED */
        .password-input-group {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 12px;
            top: 70%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #667eea;
            cursor: pointer;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 38px;
            /* Match your input height */
            width: 38px;
            line-height: 1;
        }


        /* Adjust input padding to prevent text under icon */
        .password-input-group .form-control {
            padding-right: 45px;
            /* Leave enough room for the icon */
            height: 45px;
            /* Set a fixed height for consistency */
            box-sizing: border-box;
        }

        /* Responsive adjustments */
        @media (max-width: 400px) {
            .form-section {
                padding: 1rem;
            }

            .register-inline {
                flex-direction: column;
                gap: 0.5rem;
            }

            .register-inline .form-control,
            .register-inline .btn {
                width: 100%;
            }
        }
    </style>
</head>

<body>
    <div class="branding">
        <h1><i class="fas fa-balance-scale"></i> Advanced Legal Tech Case Management System</h1>
        <p>The most professional, modern, and secure platform for legal professionals</p>
    </div>
    <div class="container">
        <div class="login-container">
            <div class="header-section">
                <h1><i class="fas fa-sign-in-alt"></i> Login</h1>
                <p class="mb-0">Access your account</p>
            </div>
            <div class="form-section">
                <button class="btn social-btn btn-email" type="button" id="showEmailLogin"><i
                        class="fas fa-envelope"></i>Login with Email</button>
                <div id="emailLoginForm"
                    style="display: {% if message or prefilled_username %}block{% else %}none{% endif %};">
                    {% if message %}
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    {% endif %}
                    <form method="POST" action="/login">
                        <div class="mb-3">
                            <label for="username" class="form-label">Username</label>
                            <input type="text" class="form-control" id="username" name="username" required
                                value="{{ prefilled_username }}" />
                        </div>
                        <div class="mb-3 password-input-group">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" name="password" required />
                            <button type="button" class="password-toggle" id="togglePassword">
                                <i class="far fa-eye"></i>
                            </button>
                        </div>
                        <div class="mb-3 custom-select">
                            <label for="user-role" class="form-label">User Role</label>
                            <select class="form-control" id="user-role" name="user_role" required>
                                <option value="" disabled selected>Select your role</option>
                                <option value="client">Client</option>
                                <option value="attorney">Attorney</option>
                                <option value="employee">Employee</option>
                            </select>
                        </div>
                        <div class="d-grid">
                            <button id="login" type="submit" class="btn btn-primary"><i
                                    class="fas fa-sign-in-alt me-2"></i>Login</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="register-card">
            <h2><i class="fas fa-user-plus"></i> New User?</h2>
            <p class="mb-2">Select your role to register:</p>
            <form method="GET" action="/register" class="register-inline">
                <div class="custom-select">
                    <select class="form-control" id="new-user-role" name="role" required style="max-width: 220px;">
                        <option value="" disabled selected>Select your role</option>
                        <option value="client">Client</option>
                        <option value="attorney">Attorney</option>
                        <option value="employee">Employee</option>
                    </select>
                </div>
                <button type="submit" class="btn btn-outline-primary"><i
                        class="fas fa-user-plus me-2"></i>Register</button>
            </form>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const emailLoginForm = document.getElementById('emailLoginForm');
            const showEmailLogin = document.getElementById('showEmailLogin');

            // Show form if there's an error message or prefilled username
            if (emailLoginForm.innerHTML.includes('alert-danger') ||
                document.getElementById('username').value) {
                emailLoginForm.style.display = 'block';
                showEmailLogin.style.display = 'none';
            }

            // Toggle form visibility
            showEmailLogin.addEventListener('click', function () {
                emailLoginForm.style.display = 'block';
                this.style.display = 'none';
            });

            // Password toggle functionality
            const togglePassword = document.getElementById('togglePassword');
            const password = document.getElementById('password');

            if (togglePassword && password) {
                togglePassword.addEventListener('click', function () {
                    const type = password.getAttribute('type') === 'password' ? 'text' : 'password';
                    password.setAttribute('type', type);
                    this.innerHTML = type === 'password'
                        ? '<i class="far fa-eye"></i>'
                        : '<i class="far fa-eye-slash"></i>';
                });

                // Initialize with proper icon
                togglePassword.innerHTML = password.getAttribute('type') === 'password'
                    ? '<i class="far fa-eye"></i>'
                    : '<i class="far fa-eye-slash"></i>';
            }
        });
    </script>
</body>

</html>