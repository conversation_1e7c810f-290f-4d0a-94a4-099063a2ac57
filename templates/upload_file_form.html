<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload File</title>
    <style>
        /* Form styling */
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        #uploadForm {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        
        /* Form elements styling */
        label {
            display: block;
            margin-top: 15px;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        select, input[type="file"], input[type="text"] {
            width: 100%;
            padding: 10px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        select {
            height: 40px;
        }
        
        button[type="submit"] {
            display: block;
            width: 100%;
            padding: 12px;
            margin-top: 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        
        button[type="submit"]:hover {
            background-color: #45a049;
        }
        
        /* Message container */
        #messageContainer {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        
        /* Responsive adjustments */
        @media (max-width: 600px) {
            #uploadForm {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <form id="uploadForm" method="POST" enctype="multipart/form-data">
        <!-- Client Dropdown -->
        <label for="client_name">Select Client:</label>
        <select id="client_name" name="client_name" required>
            <option value="">--Select Client--</option>
            <!-- Clients populated dynamically -->
        </select>

        <!-- Case Dropdown -->
        <label for="case_name">Select Case:</label>
        <select id="case_name" name="case_name" required>
            <option value="">--Select Case--</option>
            <!-- Cases populated dynamically -->
        </select>

        <!-- Document Type Dropdown -->
        <label for="document-type">Document Type:</label>
        <select id="document-type" name="document_type" required>
            <option value="">--Select Document Type--</option>
            <!-- Document types populated dynamically -->
        </select>

        <!-- Custom Type Input (Hidden by Default) -->
        <div id="custom-type-input" style="display: none;">
            <label for="custom-type">Enter Custom Type:</label>
            <input type="text" id="custom-type" name="custom_type" placeholder="e.g., Witness Statements">
        </div>

        <!-- File Input -->
        <label for="file">Choose File:</label>
        <input type="file" id="file" name="file" required>

        <!-- Submit Button -->
        <button type="submit">Upload File</button>

        <!-- Message Container -->
        <div id="messageContainer"></div>
    </form>

    <!-- Include JavaScript files -->
    <script type="module" src="{{ url_for('static', filename='js/upload_file_message.js') }}"></script>
    {% include "partials/posthog.html" %}
</body>
</html>