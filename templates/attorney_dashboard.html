<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Attorney Dashboard</title>
  <!-- Font Awesome for icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
  <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}" />
</head>

<body>
  <!-- Top Navigation Bar -->
  <nav class="top-nav">
    <div class="nav-container">
      <button class="menu-toggle" id="menuToggle">
        <i class="fas fa-bars"></i>
      </button>
      <div class="nav-brand">
        <i class="fas fa-balance-scale"></i>
        <span>LawFirm Pro</span>
      </div>
      <div class="nav-links">
        <a href="/dashboard" class="active"><i class="fas fa-file-alt"></i>Case Dashboard</a>
        <a href="/calendar"><i class="fas fa-calendar-alt"></i> Calendar</a>
        <a href="#"><i class="fas fa-file-alt"></i> Documents</a>
        <a href="#"><i class="fas fa-file-invoice-dollar"></i> Billing</a>
        <div class="user-dropdown">
          <button class="user-btn">
            <i class="fas fa-user-circle"></i>
            <span>{{ username }}</span>
            <i class="fas fa-chevron-down"></i>
          </button>
          <div class="dropdown-content">
            <a href="#"><i class="fas fa-user"></i> Profile</a>
            <a href="#"><i class="fas fa-cog"></i> Settings</a>
            <a href="/logout"><i class="fas fa-sign-out-alt"></i> Logout</a>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <div class="main-container">
    <!-- Sidebar Navigation -->
    <aside class="sidebar">
      <div class="sidebar-header">
        <h3>Navigation</h3>
      </div>
      <ul class="sidebar-menu">
        <li class="active">
          <a href="#clients-tab" class="tab-link" data-tab="clients-tab">
            <i class="fas fa-users"></i> Clients
          </a>
        </li>
        <li>
          <a href="#cases-tab" class="tab-link" data-tab="cases-tab">
            <i class="fas fa-folder-open"></i> Cases
          </a>
        </li>
        <li>
          <a href="#contacts-tab" class="tab-link" data-tab="contacts-tab">
            <i class="fas fa-address-book"></i> Contacts
          </a>
        </li>
        <li>
          <a href="#overview-tab" class="tab-link" data-tab="overview-tab">
            <i class="fas fa-chart-pie"></i> Overview
          </a>
        </li>
        <li>
          <a href="/calendar">
            <i class="fas fa-calendar-alt"></i> Calendar
          </a>
        </li>
        <li>
          <a href="#"> <i class="fas fa-file-contract"></i> Documents </a>
        </li>
        <li>
          <a href="#"> <i class="fas fa-clock"></i> Time Tracking </a>
        </li>
        <li>
          <a href="/support"> <i class="fas fa-headset"></i> Support </a>
        </li>
      </ul>
    </aside>

    <!-- Main Content Area -->
    <main class="content-area">
      <header class="dashboard-header">
        <h1>Attorney Dashboard</h1>
        <p>Welcome back, {{ username }}! <span id="current-date"></span></p>
      </header>

      <div class="dashboard-container">
        <!-- Clients Section -->
        <section id="clients-tab" class="tab-content active">
          <div class="section-header">
            <h2><i class="fas fa-users"></i> Clients</h2>
            <div class="section-actions">
              <button class="btn btn-primary" id="add-client-btn">
                <i class="fas fa-plus"></i> Add Client
              </button>
              <div class="search-box">
                <i class="fas fa-search"></i>
                <input type="text" id="client-search" placeholder="Search clients..." />
              </div>
            </div>
          </div>
          <div class="section-body">
            <div class="table-responsive">
              <table id="clients-table">
                <thead>
                  <tr>
                    <th>Name</th>
                  </tr>
                </thead>
                <tbody id="clients-list">
                  <!-- Dynamically populated -->
                </tbody>
              </table>
            </div>
          </div>
        </section>

        <!-- Cases Section -->
        <section id="cases-tab" class="tab-content">
          <div class="section-header">
            <h2><i class="fas fa-folder-open"></i> Cases</h2>
            <div class="section-actions">
              <button class="btn btn-primary" id="add-case-btn">
                <i class="fas fa-plus"></i> New Case
              </button>
              <div class="search-box">
                <i class="fas fa-search"></i>
                <input type="text" id="case-search" placeholder="Search cases..." />
              </div>
            </div>
          </div>
          <div class="section-body">
            <div class="case-layout">
              <div class="case-list-container">
                <div class="case-filters">
                  <div class="filter-group">
                    <label>Status:</label>
                    <select id="case-status-filter">
                      <option value="all">All</option>
                      <option value="open">Open</option>
                      <option value="closed">Closed</option>
                    </select>
                  </div>
                  <div class="filter-group">
                    <label>Type:</label>
                    <select id="case-type-filter">
                      <option value="all">All</option>
                      <option value="litigation">Litigation</option>
                      <option value="transactional">Transactional</option>
                      <option value="family">Family</option>
                      <option value="criminal">Criminal</option>
                    </select>
                  </div>
                </div>
                <ul id="cases-list" class="case-list">
                  <!-- Dynamically populated -->
                </ul>
              </div>
            </div>
          </div>
        </section>

        <!-- Contacts Section -->
        <section id="contacts-tab" class="tab-content">
          <div class="section-header">
            <h2><i class="fas fa-address-book"></i> Contacts</h2>
            <div class="section-actions">
              <button class="btn btn-primary" id="add-contact-btn">
                <i class="fas fa-plus"></i> Add Contact
              </button>
              <div class="search-box">
                <i class="fas fa-search"></i>
                <input type="text" id="contact-search" placeholder="Search contacts..." />
              </div>
            </div>
          </div>
          <div class="section-body">
            <div class="table-responsive">
              <table id="contacts-table">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Type</th>
                    <th>Email</th>
                    <th>Phone</th>
                    <th>Case Link</th>
                    <th>Last Contact</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <!-- Dynamically populated -->
                </tbody>
              </table>
            </div>
          </div>
        </section>

        <!-- Overview Section -->
        <section id="overview-tab" class="tab-content">
          <div class="section-header">
            <h2><i class="fas fa-chart-pie"></i> Dashboard Overview</h2>
            <div class="section-actions">
              <button class="btn btn-secondary" id="refresh-dashboard">
                <i class="fas fa-sync-alt"></i> Refresh
              </button>
            </div>
          </div>
          <div class="section-body">
            <div class="overview-grid">
              <div class="overview-card" id="active-cases">
                <div class="card-header">
                  <h3><i class="fas fa-folder-open"></i> Active Cases</h3>
                  <a href="/dashboard" class="view-all">View All</a>
                </div>
                <div class="card-content">
                  <div class="loading-spinner">
                    <div class="spinner"></div>
                    <p>Loading active cases...</p>
                  </div>
                </div>
              </div>

              <div class="overview-card" id="deadlines">
                <div class="card-header">
                  <h3>
                    <i class="fas fa-calendar-day"></i> Upcoming Deadlines
                  </h3>
                  <a href="/calendar" class="view-all">View Calendar</a>
                </div>
                <div class="card-content" id="deadlines-list">
                  <div class="loading-spinner">
                    <div class="spinner"></div>
                    <p>Loading deadlines...</p>
                  </div>
                </div>
              </div>

              <div class="overview-card" id="notifications">
                <div class="card-header">
                  <h3><i class="fas fa-bell"></i> Notifications</h3>
                  <button class="mark-all-read">Mark All Read</button>
                </div>
                <div class="card-content">
                  <div class="loading-spinner">
                    <div class="spinner"></div>
                    <p>Loading notifications...</p>
                  </div>
                </div>
              </div>

              <div class="overview-card" id="document-activity">
                <div class="card-header">
                  <h3><i class="fas fa-file-upload"></i> Recent Documents</h3>
                  <a href="/documents" class="view-all">View All</a>
                </div>
                <div class="card-content">
                  <div class="loading-spinner">
                    <div class="spinner"></div>
                    <p>Loading documents...</p>
                  </div>
                </div>
              </div>

              <div class="overview-card" id="quick-actions">
                <div class="card-header">
                  <h3><i class="fas fa-bolt"></i> Quick Actions</h3>
                </div>
                <div class="card-content quick-actions-grid">
                  <!-- Event Button -->
                  <button class="quick-action-btn" onclick="showQuickActionModal('event')">
                    <i class="fas fa-calendar-check"></i> Event
                  </button>

                  <!-- Document Button -->
                  <button class="quick-action-btn" onclick="showQuickActionModal('document')">
                    <i class="fas fa-file-alt"></i> Document
                  </button>

                  <!-- Task Button with Popup -->
                  <button class="quick-action-btn" onclick="showTaskModal()">
                    <i class="fas fa-tasks"></i> Task
                  </button>

                  <!-- Message Button -->
                  <button class="quick-action-btn" onclick="showQuickActionModal('message')">
                    <i class="fas fa-envelope"></i> Message
                  </button>

                  <!-- Time Entry Button -->
                  <button class="quick-action-btn" onclick="showQuickActionModal('time')">
                    <i class="fas fa-stopwatch"></i> Time Entry
                  </button>
                  <button class="quick-action-btn" onclick="showDeadlineModal()">
                    <i class="fas fa-calendar-plus"></i> Deadline
                  </button>

                  <!-- Expense Button -->
                  <button class="quick-action-btn" onclick="showQuickActionModal('expense')">
                    <i class="fas fa-receipt"></i> Expense
                  </button>

                  <!-- Invoice Button -->
                  <button class="quick-action-btn" onclick="showQuickActionModal('invoice')">
                    <i class="fas fa-file-invoice-dollar"></i> Invoice
                  </button>

                  <!-- Note Button -->
                  <button class="quick-action-btn" onclick="showQuickActionModal('note')">
                    <i class="fas fa-sticky-note"></i> Note
                  </button>
                </div>
              </div>

                <div class="overview-card" id="time-tracking">
                  <div class="card-header">
                    <h3><i class="fas fa-clock"></i> Time This Week</h3>
                    <a
                      href="javascript:void(0)"
                      class="view-all"
                      onclick="showTimeDetailsModal()"
                      >Details</a
                    >
                  </div>
                  <div class="card-content">
                    <div class="time-summary">
                      <div class="time-metric">
                        <span class="time-value">12.5</span>
                        <span class="time-label">hours</span>
                      </div>
                      <div class="time-chart">
                        <!-- Placeholder for chart -->
                        <div class="chart-placeholder"></div>
                      </div>
                    </div>
                  </div>
                </div>

              <div class="overview-card" id="billing-summary">
                <div class="card-header">
                  <h3><i class="fas fa-dollar-sign"></i> Billing Summary</h3>
                  <a href="javascript:void(0)" class="view-all" onclick="showReportModal()">View Reports</a>
                </div>
                <div class="card-content">
                  <div class="billing-stats">
                    <div class="stat-item">
                      <span class="stat-value">$8,450</span>
                      <span class="stat-label">This Month</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-value">$42,300</span>
                      <span class="stat-label">Quarter to Date</span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="overview-card" id="client-overview">
                <div class="card-header">
                  <h3><i class="fas fa-user-tie"></i> Client Overview</h3>
                </div>
                <div class="card-content">
                  <div class="client-stats">
                    <div class="stat-item">
                      <span class="stat-value">24</span>
                      <span class="stat-label">Active Clients</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-value">5</span>
                      <span class="stat-label">New This Month</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>

      <div id="taskModal" class="quick-action-modal">
        <div class="modal-content">
          <span class="close-btn" onclick="closeModal('taskModal')">&times;</span>
          <h3><i class="fas fa-tasks"></i> Create New Task</h3>
          <form id="taskForm">
            <div class="form-group">
              <label for="taskCaseId">Case:</label>
              <select id="taskCaseId" required>
                <!-- Options will be populated by JavaScript -->
              </select>
            </div>
            <div class="form-group">
              <label for="taskType">Task Type:</label>
              <input type="text" id="taskType" required />
            </div>
            <div class="form-group">
              <label for="taskDescription">Description:</label>
              <textarea id="taskDescription"></textarea>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label for="taskDueDate">Due Date:</label>
                <input type="datetime-local" id="taskDueDate" />
              </div>
              <div class="form-group">
                <label for="taskPriority">Priority:</label>
                <select id="taskPriority">
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                  <option value="low">Low</option>
                </select>
              </div>
            </div>
            <button type="submit" class="submit-btn">Create Task</button>
          </form>
        </div>
      </div>

      <!-- Deadline Modal -->
      <div id="deadlineModal" class="quick-action-modal">
        <div class="modal-content">
          <span class="close-btn" onclick="closeModal('deadlineModal')">&times;</span>
          <h3><i class="fas fa-calendar-plus"></i> Add Deadline</h3>
          <form id="deadlineForm">
            <div class="form-group">
              <label for="deadlineCaseId">Case:</label>
              <select id="deadlineCaseId" required>
                <!-- Options will be populated by JavaScript -->
              </select>
            </div>
            <div class="form-group">
              <label for="deadlineType">Deadline Type:</label>
              <input type="text" id="deadlineType" required />
            </div>
            <div class="form-group">
              <label for="deadlineDate">Date:</label>
              <input type="datetime-local" id="deadlineDate" required />
            </div>
            <div class="form-group">
              <label for="deadlineNotes">Notes:</label>
              <textarea id="deadlineNotes"></textarea>
            </div>
            <div class="form-group">
              <label for="isCourtDate">
                <input type="checkbox" id="isCourtDate" /> Court Date
              </label>
            </div>
            <button type="submit" class="submit-btn">Add Deadline</button>
          </form>
        </div>
      </div>

      <!-- Report Modal -->
      <div id="reportModal" class="quick-action-modal">
        <div class="modal-content">
          <span class="close-btn" onclick="closeModal('reportModal')">&times;</span>
          <h3><i class="fas fa-chart-line"></i> Billing Reports</h3>
          <div class="report-content">
            <p><strong>Total Billed This Month:</strong> $8,450</p>
            <p><strong>Quarter to Date:</strong> $42,300</p>
            <p><strong>Outstanding Invoices:</strong> $1,200</p>
            <p><strong>Paid Invoices:</strong> $39,000</p>
          </div>
        </div>
      </div>

        <!-- General Quick Action Modal -->
        <div id="quickActionModal" class="quick-action-modal">
          <div class="modal-content">
            <span class="close-btn" onclick="closeModal('quickActionModal')"
              >&times;</span
            >
            <h3 id="quickActionTitle"></h3>
            <div id="quickActionContent">
              <!-- Content will be loaded dynamically -->
            </div>
          </div>
        </div>
      </main>
    </div>
    <!-- Time Details Modal -->
    <div id="timeDetailsModal" class="quick-action-modal">
      <div class="modal-content">
        <span class="close-btn" onclick="closeModal('timeDetailsModal')"
          >&times;</span
        >
        <h3><i class="fas fa-clock"></i> Time Tracking This Week</h3>
        <div class="modal-body">
          <ul style="list-style-type: none; padding: 0">
            <li><strong>Monday:</strong> 3.0 hrs - Client Meeting</li>
            <li><strong>Tuesday:</strong> 2.5 hrs - Case Research</li>
            <li><strong>Wednesday:</strong> 1.5 hrs - Drafting</li>
            <li><strong>Thursday:</strong> 2.0 hrs - Court Appearance</li>
            <li><strong>Friday:</strong> 3.5 hrs - Document Review</li>
          </ul>
        </div>
      </div>
    </div>

    <script>
      function showTimeDetailsModal() {
        document.getElementById("timeDetailsModal").style.display = "block";
      }
    </script>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
  <script type="module" src="{{ url_for('static', filename='js/attorney_dashboard.js') }}"></script>
  <!-- Add this script tag right before your closing </body> tag -->
  <script>

    // Mobile menu toggle
    document.addEventListener("DOMContentLoaded", () => {
      const menuToggle = document.getElementById("menuToggle");
      const navLinks = document.querySelector(".nav-links");
      const sidebarToggle = document.createElement("button");

      // Create sidebar toggle button for mobile
      sidebarToggle.innerHTML = '<i class="fas fa-bars"></i>';
      sidebarToggle.className = 'sidebar-toggle';
      sidebarToggle.style.position = 'fixed';
      sidebarToggle.style.top = '70px';
      sidebarToggle.style.left = '10px';
      sidebarToggle.style.zIndex = '1000';
      sidebarToggle.style.background = 'var(--primary-color)';
      sidebarToggle.style.color = 'white';
      sidebarToggle.style.border = 'none';
      sidebarToggle.style.borderRadius = '50%';
      sidebarToggle.style.width = '40px';
      sidebarToggle.style.height = '40px';
      sidebarToggle.style.display = 'none';

      document.body.appendChild(sidebarToggle);

      // Toggle mobile menu
      menuToggle?.addEventListener("click", () => {
        navLinks.classList.toggle("active");
        // Close sidebar if open when opening mobile menu
        document.querySelector(".sidebar").classList.remove("active");
        sidebarToggle.style.display = 'none';
      });

      // Toggle sidebar
      sidebarToggle.addEventListener("click", () => {
        document.querySelector(".sidebar").classList.toggle("active");
      });

      // Show/hide sidebar toggle based on screen size
      function handleResponsive() {
        if (window.innerWidth <= 992) {
          sidebarToggle.style.display = 'block';
        } else {
          sidebarToggle.style.display = 'none';
          document.querySelector(".sidebar").classList.remove("active");
          navLinks.classList.remove("active");
        }
      }

      // Initial check
      handleResponsive();

      // Listen for resize events
      window.addEventListener("resize", handleResponsive);

      // Close menus when clicking outside
      document.addEventListener("click", (e) => {
        if (window.innerWidth <= 992) {
          if (!e.target.closest(".nav-container") && !e.target.closest(".sidebar-toggle")) {
            navLinks.classList.remove("active");
          }
          if (!e.target.closest(".sidebar") && !e.target.closest(".sidebar-toggle")) {
            document.querySelector(".sidebar").classList.remove("active");
          }
        }
      });
    });
    function showReportModal() {
      document.getElementById("reportModal").style.display = "block";
    }

    function closeModal(modalId) {
      document.getElementById(modalId).style.display = "none";
    }
  </script>
</body>

</html>